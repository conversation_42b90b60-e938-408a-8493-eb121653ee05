spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      driver-class-name: com.mysql.jdbc.Driver
      url: ****************************************************************************************************************************************************************************************
      username: bonc
      password: Bonc1234*
      initial-size: 10
      max-active: 100
      min-idle: 10
      max-wait: 60000
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      #Oracle需要打开注释
      #validation-query: SELECT 1 FROM DUAL
      log-abandoned: true
      remove-abandoned: true
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        login-username: admin
        login-password: admin
      filter:
        stat:
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: false
        wall:
          config:
            multi-statement-allow: true

  redis:
    open: true  # 是否开启redis缓存  true开启   false关闭
    database: 0
    host: r-uf6ctchwapkrzmvgpx.redis.rds.aliyuncs.com
    port: 6379
    password: Rrs@admin   # 密码（默认为空）
    timeout: 6000ms  # 连接超时时长（毫秒）
    lettuce:
      pool:
        max-active: 1000  # 连接池最大连接数（使用负值表示没有限制）
        max-wait: -1ms      # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-idle: 1000      # 连接池中的最大空闲连接
        min-idle: 200       # 连接池中的最小空闲连接
send-msg:
  enable: true
ip:
  address: *************,************,*************

oss:
  bucketName: ah-rrs
baseUrl: https://gd.rrskjfw.com.cn/

# 阿里云人脸比对配置
aliyun:
  face:
    accessKeyId: ${ALIBABA_CLOUD_ACCESS_KEY_ID:your_access_key_id}
    accessKeySecret: ${ALIBABA_CLOUD_ACCESS_KEY_SECRET:your_access_key_secret}
    endpoint: facebody.cn-shanghai.aliyuncs.com
    regionId: cn-shanghai

dataType: mysql
##多数据源的配置
dynamic:
  datasource:
    slave1:
      driver-class-name: com.mysql.jdbc.Driver
      url: ******************************************************************************************************************************************
      username: yhxx
      password: Yhxx@123
      #超过时间限制是否回收
      remove-abandoned: true
      #超时时间；单位为秒
      remove-abandoned-timeout: 1800
#    slave2:
#      driver-class-name: org.postgresql.Driver
#      url: ************************************************
#      username: renren
#      password: 123456

logging:
  level:
    com.bonc.rrs.worder.dao: debug
    com.bonc.rrs.worderapp.dao: debug
    com.bonc.rrs.workManager.dao: debug
    com.bonc.rrs.supervise.dao: debug
    com.bonc.rrs.worderinformationaccount.dao: debug
    com.bonc.rrs.branchbalance.dao.BranchBalanceDao: debug
    com.bonc.rrs.util.orderlog: debug
  path: /mnt2/rrs-8081/logs/springlog

#wsdl配置文件
# 360额度
quotawsdl: prodwsdl/TransEffectiveLimitFromCBSTo360.wsdl
quotalocation: classpath:prodwsdl/TransEffectiveLimitFromCBSTo360.wsdl
# sap余额
sapwsdl: prodwsdl/TransSupplierBalanceFromEVSToSAP.wsdl
saplocation: classpath:prodwsdl/TransSupplierBalanceFromEVSToSAP.wsdl
# 金税
headwsdl: prodwsdl/InsertLSMallZssHead.wsdl
headlocation: classpath:prodwsdl/InsertLSMallZssHead.wsdl
itemwsdl: prodwsdl/InsertLSMallZssItemNew.wsdl
itemlocation: classpath:prodwsdl/InsertLSMallZssItemNew.wsdl
zsszfwsdl: prodwsdl/InsertXwsqZssZF.wsdl
zsszflocation: classpath:prodwsdl/InsertXwsqZssZF.wsdl
cacelwsdl: prodwsdl/QueryCancelInterface.wsdl
cacelocation: classpath:prodwsdl/QueryCancelInterface.wsdl
caceltable: RWKJ.xwsq_zzs_zfhx
table: RWKJ.xwsq_zzs_kphx
# acs
acsurl: http://58.56.128.10:19001/EAI/service/ACS/TransInfoToACSPost/TransInfoToACSPost?INT_CODE=EAI_INT_2552
#商户通
busicvp: http://cvp.haier.net/services/busiCommon/Busi_Common_Srv?wsdl
hcspcvp: http://cvp.haier.net/services/hcsp/HCSP_Sheet_Srv?wsdl
#海尔能力平台接口配置
hscapi:
  application:
    #系统编码
    key: 5f3e05613722cc046cfae6dbd3922743
    #系统秘钥
    secret: 5a00de01bebda4332d4bebccbfc6f6ca
  #票税云
  blade-seller-business:
    sellerinvoice:
      #票税云开票申请
      setXwsqZzsHeadVO:
        url: http://hscapi.haier.net/blade-seller-business/seller-apply-master/setXwsqZzsHeadVO
        methodName: setXwsqZzsHeadVO
      #票税云开票结果查询
      getSellerInvoice:
        url: http://hscapi.haier.net/blade-seller-business/sellerinvoice/getSellerInvoice
        methodName: getSellerInvoice
      #税票云凭证归档
      setXwsqZzsArchive:
        url: http://hscapi.haier.net/blade-seller-business/sellerinvoice/setXwsqZzsArchive
        methodName: setXwsqZzsArchive

finance:
  business:
    appId: news
    key: XA12#Da
    insertOrUpdateUrl: http://cw.rrskjfw.com.cn/prod-api/finance/carMergerOrder/insertOrUpdate
    deleteOrderUrl: http://cw.rrskjfw.com.cn/prod-api/finance/carMergerOrder/deleteOrder
    transferSubOrderUrl: http://cw.rrskjfw.com.cn/prod-api/finance/details/transferSubOrder
    detailsEditUrl: http://cw.rrskjfw.com.cn/prod-api/finance/details/edit
    invoiceEditUrl: http://cw.rrskjfw.com.cn/prod-api/finance/headers/invoiceEdit
    searchOrderUrl: http://cw.rrskjfw.com.cn/prod-api/finance/carMergerOrder/selectOrder
    transferApprovalFormUrl: http://cw.rrskjfw.com.cn/prod-api/finance/approval/transferApprovalForm
    selectSettlementStatusUrl: http://cw.rrskjfw.com.cn/prod-api/lfs/hcsp/selectSettlementStatus
    settlementCancelUrl: http://cw.rrskjfw.com.cn/prod-api/lfs/hcsp/SettlementCancel
    newsDataAccessUrl: http://cw.rrskjfw.com.cn/prod-api/lfs/hcsp/newsDataAccess
    settleAuditAddUrl: http://cw.rrskjfw.com.cn/prod-api/settleAudit/add
    advAuditAddUrl: http://cw.rrskjfw.com.cn/prod-api/lfs/advAudit/add
    lfsSettleAuditAddUrl: http://cw.rrskjfw.com.cn/prod-api/lfs/settleAudit/add
    acAddUrl: http://cw.rrskjfw.com.cn/prod-api/finance/ac/add
    newAdvanceSettlementUrl: http://cw.rrskjfw.com.cn/prod-api/fncSettleAuditNew/newAdvanceSettlement
    callback:
      appId: finance
      appSecret: hrlLLGEaShHP6yE72he1l45T8jj7SVuU
storage:
  business:
    appId: storage
    secret: hZur4Kac
    leaveStoreUrl: https://gd.rrskjfw.com.cn/rrs-storage/smt/RRSGoodsLeaveOrder/leaveStoreIntf
    handleOutCompleteUrl: https://gd.rrskjfw.com.cn/rrs-storage/smt/RRSGoodsLeaveOrder/handleOutCompleteIntf
    handleInvalidUrl: https://gd.rrskjfw.com.cn/rrs-storage/smt/RRSGoodsLeaveOrder/handleInvalidIntf
byd:
  url: https://cpim.byd.com
  AppSecret: k1982eu9vtrooj9ehjwzh2gtmu2876cn
  APP_KEY: rnfkw51hjk87f3ur8932h8d38g233h65
xk:
  url: https://xkdg.prod.pc.rrskjfw.com.cn/prod-api
  AppSecret: k1982eu9vtrooj9ehjwzh2gtmu2876cn
  APP_KEY: rnfkw51hjk87f3ur8932h8d38g233h65

# 长安接口地址
cacfg:
  ACC_KEY: 2NS5XFLJ91AKMSN90776
  SECRE_KEY: 2BJZTFJGC89103M2RZAFARNQXV7ZGFO9
  SUPPLIER_CODE: *********
  baseUrl: https://dtd.changan.com.cn/gateway/api/
  ##车企离线对接
bridgehub:
  jlUrl: https://sp-recharge.geely.com/InstallManage/orderDetail/
  url: http://10.10.10.128:8080/bridgehub/
  token: aaaaaa
  paramToken: 4df6999b3e91466ca9e195b55b205ca4


honeywell:
  # prod
  baseUrl: https://dms-api.hopeful.company
  client_id: dmj_system
  client_secret: x93hzuAXBR56Xfnv1
  grant_type: client_credentials password_sms
  scope: read

news:
  gace:
    app-id: DMJ
    app-secret: DMJ@202410150001123a
    base-url: https://srm.gace.com.cn/ierp
    user: 12341111234
    id-number: HERRS
    page-size: 50
    company-id: 684
    query-hours: 3
    cancel-days: 1
    audit-days: 30
    field:
      woop-id: 1988
      asset-id: 1989
      woop-no: 1990
      act-finish-time: 1197
      act-check-time: 1062
      fault-tree-type: 2033
    tel: 400-9219898
    template-ids:
      - 713
      - 714
      - 715
      - 716
      - 717
      - 826
      - 827
      - 980
      - 1017
    woop-wcp-id-number: DMJ
    repair-template-id: 378
    amap-param:
      key: d3f2628800113145b90dd8c81df8e3dd
      url: https://restapi.amap.com/v3/geocode/geo
  lx:
    report-temp-path: /mnt2/file/

changcheng:
  persistentCcToken: J1MizRUsq3pG0JeqomCDz6rY08GipEvM
  baseUrl: https://energy-jzdd-api.gwm.com.cn
  supplierCode: 2777674c-1cdd-4557-8256-89b1788e1b76
  field:
    vinId: 2333 # 车架号
    mallOrderNoId: 2332 # 商城订单号
    dtcOrderNoId: 2334 # DTC 客户订单号
    endSubmitTimeId: 2349 # 安装权益到期时间
    carOwnerId: 2335 # 车主
    carOwnerPhoneId: 2336 # 车主联系方式
    installContactId: 2337 # 安装联系人
    installContactPhoneId: 2338 # 安装联系人电话
    orderRemarkId: 2339 # 订单备注字段ID
    dispatchTimeId: 2340 # 派单时间字段ID
    dispatchRemarkId: 2353 # 派单备注字段ID
    elecTypeId: 2341 # 用电类型字段ID
    checkRemarkId: 2350 # 勘测结果
    checkFinishTimeId: 2351 # 勘测完成时间
    hasPowerInstallId: 2342 # 是否具备安装条件
    noInstallConditionsId: 2352 # 不具备安装条件说明
    wireLengthId: 2344 # 布线距离
    installTypeId: 2346 # 铺设方式
    actualPriceId: 2345 # 客户支付费用
    pileModelNameId: 2347 # 充电桩型号
    installFinishTimeId: 2343 #安装完成时间
    pileCodeId: 2348 #充电桩编码

    personPilePhotoId: 1849 # 03: 人桩合影
    personPilePhotoId2: 1419 # 03: 人桩合影
    surveyAttachmentId: 1861  # 13: 勘测及其它附件
    surveyAttachmentId2: 1823  # 13: 勘测及其它附件
    surveyAttachmentId3: 1853  # 13: 长城--线路走向
    surveyAttachmentId4: 1854  # 13: 长城--工具摆放
    surveyAttachmentId5: 1855  # 13: 长城--模拟充电测试
    surveyAttachmentId6: 1856  # 13: 长城--电缆起止米标
    surveyAttachmentId7: 1857  # 13: 长城--电缆标识
    surveyAttachmentId8: 1858  # 13: 长城--绝缘电阻测试
    surveyAttachmentId9: 1859  # 13: 长城--接地电阻测试
    surveyAttachmentId10: 1860  # 13: 长城--增项项目施工照片
    additionalQuoteId: 1851  # 17: 增项报价单
    protectionSwitchId: 1852  # 18: 保护开关
    installationAcceptanceId: 1848  # 19: 安装竣工验收表
    chargingPileCodeId: 1421 # 20: 充电桩编号
    chargingPilePlateId: 1850 # 20: 充电桩铭牌
    powerPointId: 1862  # 22: 电源点
    cableMarkId: 1857 # 23: 线缆标识




leapmotor:
  appId: DMJ9d4fG
  appSecret: eHSYqp6D1G8suOkSADkweA==
  baseUrl: https://psg.leapmotor.com
  field:
    vinId: 153 # 车架号
    orderNumber: 940 # 车企系统订单编号
    operationTimeCreate: 154 # 车企派单日期
    productOrderNumber: 2293 # 零跑-商品订单号
    customerName: 902 # 客户姓名
    customerPhone: 905 # 客户手机
    orderType: 2296 # 零跑-订单类型
    requiredProduct: 2297 # 零跑-需求产品
    requiredProductModel: 0 # 零跑-需求产品型号（生产无对应）
    submissionDate: 0 # 零跑-提交日期（生产无对应）
    carModel: 0 # 零跑-车型（生产无对应）
    packageType: 2301 # 零跑-套餐类型
    remark: 2302 # 零跑-备注-创建
    totalCableLength: 301 # 预计总布线长度
    installationMethod: 913 # 安装方式
    measuredVoltage: 295 # 实测电压
    surveyCompletionDate: 1196 # 实际勘测完成日期
    additionalChargeContent: 917 # 增项收费内容
    serviceStartTime: 2312 # 零跑-服务商首联时间
    estimatedUseProduct: 2311 # 零跑-预计使用产品
    parkingSpace: 2310 # 零跑-车位
    powerPoint: 2309 # 零跑-取电点
    installCondition: 2308 # 零跑-是否具备安装条件
    expectedPilingTime: 2307 # 零跑-预计收桩时间
    expectedDispatchTime: 2306 # 零跑-预计发桩时间
    bringPileOnSite: 2305 # 零跑-是否带桩上门
    noteSurvey: 2304 # 零跑-备注-勘测
    operationTimeSurvey: 2303 # 零跑-操作时间-勘测
    surveyReport: 263 # 勘测报告
    powerPointPhoto: 264 # 电源点照片
    parkingSpaceMap: 265 # 车位图
    other: 267 # 其他
    disclaimer: 2325 # 零跑-免责申明
    userNotification: 2324 # 零跑-用户告知书
    surveyForm: 2323 # 零跑-勘测表
    chargingPileCode: 1409 # 领跑充电桩编码
    installationMethodType: 1262 # 敷设方式
    groundResistance: 304 # 接地电阻
    chargingPileModel: 951 # 充电桩型号
    measuredVoltageValue: 927 # 实测电压值（L-N）
    parkingSpaceAddress: 936 # 车位地址
    powerSourceMethod: 920 # 取电方式
    installationMethodType2: 921 # 充电桩安装方式
    arrivalTime: 2322 # 零跑-实际上门时间
    serviceAppointmentTime: 2321 # 零跑-服务人员预约上门时间
    expectedVisitTime: 2320 # 零跑-客户期望上门时间
    installationConditionTime: 2319 # 零跑-客户具备安装条件时间
    grounding: 2318 # 零跑-是否接地
    actualProductUsed: 2317 # 零跑-实际使用产品
    extraAmountPaidOnSite: 2316 # 零跑-客户现场额外支付金额
    cableLengthUsed: 2315 # 零跑-实际使用线缆米数
    installationCompletionTime: 2314 # 零跑-实际安装完成时间
    installationOperationTime: 2313 # 零跑-操作时间-安装
    powerPointMap: 0 # 电源点图（生产无对应）
    leakageBreakerDiagram: 0 # 漏电保护断路器图（生产无对应）
    pileControlPhoto: 0 # 零跑-绑桩控桩照片图（生产无对应）
    personPilePhoto: 0 # 人桩合照（生产无对应，2326 打包）
    installationConfirmation: 0 # 安装确认书（生产无对应，2327 打包）
    cableLabelDiagram: 973 # 电缆线标识图
    chargingPileLabel: 0 # 充电桩-编号（生产无对应）
    testChargingDiagram: 0 # 模拟充电测试图（生产无对应）
    cableMeterStartDiagram: 278 # 线缆米标图-起始
    cableMeterEndDiagram: 310 # 线缆米标图-终点
    installationCableRoute: 0 # 走线图-安装（生产无对应）
    additionalChargeQuote: 0 # 增项报价单-安装（生产无对应）
    surveyReportInstallation: 0 # 勘测报告-安装（生产无对应）
    installationVideo: 0 # 安装视频（生产无对应）
    groundingPhoto: 2331 # 零跑-接地照片
    powerConnection: 2330 # 零跑-电源接线
    exceedStandardProject: 2329 # 零跑-超标工程
    distributionBox: 2328 # 零跑-配电箱
    acceptanceDocument: 2327 # 零跑-验收单据
    pileBody: 2326 # 零跑-桩体
    workOrderType: 1 # 工单类型
    carCompanyOrderNumber: 5 # 车企订单号
    carCompanyName: 101 # 车企名称
    workOrderSource: 306 # 工单来源
    customerName2: 902 # 客户姓名
    installationAddress: 903 # 安装地址
    customerEmail: 904 # 客户邮箱
    customerPhone2: 905 # 客户手机
    defaultSurveyForm: https://rrs-shbyd.oss-cn-shanghai.aliyuncs.com/20250915165540883_52195.jpg
    defaultUserNotice: https://rrs-shbyd.oss-cn-shanghai.aliyuncs.com/20250915165548130_96795.jpg
    defaultDisclaimer: https://rrs-shbyd.oss-cn-shanghai.aliyuncs.com/20250915165552643_85874.jpg

# API接口安全配置
api:
  security:
    # 是否启用接口安全认证
    enabled: true
    # 客户端密钥过期时间（小时）
    token-expire: 24
    # 客户端密钥配置
    clients:
      # 企业内部系统客户端 生成随机密钥
      service_card_id: q9HjWbLv5tR3yU7oP1xKzJ6mNc8gT0Xa
