spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      driver-class-name: com.mysql.jdbc.Driver
      url: ****************************************************************************************************************************************************************************************
      username: root
      password: G5#df87rt!qw
#      url: *****************************************************************************************************************************************************************************************
#      username: bonc
#      password: Bonc1234*
      initial-size: 10
      max-active: 100
      min-idle: 10
      max-wait: 60000
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      #超过时间限制是否回收
      remove-abandoned: true
      #超时时间；单位为秒
      remove-abandoned-timeout: 90
      #关闭abanded连接时输出错误日志
      log-abandoned: true
      #Oracle需要打开注释
      #validation-query: SELECT 1 FROM DUAL
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      stat-view-servlet:
        enabled: false
        url-pattern: /druid/*
        #login-username: admin
        #login-password: admin
      filter:
        stat:
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: false
        wall:
          config:
            multi-statement-allow: true

  redis:
    open: true  # 是否开启redis缓存  true开启   false关闭
    database: 0
    host: *************
    port: 6379
    password: rrsadmin   # 密码（默认为空）
    timeout: 6000ms  # 连接超时时长（毫秒）
    lettuce:
      pool:
        max-active: 1000  # 连接池最大连接数（使用负值表示没有限制）
        max-wait: -1ms      # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-idle: 1000      # 连接池中的最大空闲连接
        min-idle: 200       # 连接池中的最小空闲连接
dataType: mysql
##多数据源的配置
dynamic:
  datasource:
    slave1:
      driver-class-name: com.mysql.jdbc.Driver
      url: *****************************************************************************************************************************************
      username: root
      password: G5#df87rt!qw
      #超过时间限制是否回收
      remove-abandoned: true
      #超时时间；单位为秒
      remove-abandoned-timeout: 1800
#    slave2:
#      driver-class-name: org.postgresql.Driver
#      url: ************************************************
#      username: renren
#      password: 123456

logging:
  path: /mnt2/logs/rrs-8081/springlog
  level:
    com.bonc.rrs.worder.dao: debug
    com.bonc.rrs.worderapp.dao: debug
    com.bonc.rrs.workManager.dao: debug
    com.bonc.rrs.worderinformationaccount.dao: debug
    com.bonc.rrs.branchbalance.dao.BranchBalanceDao: debug
    com.bonc.rrs.baidumap.dao.*: debug
    org.apache.http: debug
    org.springframework.web.client: debug
    org.springframework.web.client.RestTemplate: debug


send-msg:
  enable: false

ip:
  address: ************,*************,************

oss:
  bucketName: sh-rrs

# 阿里云人脸比对配置
aliyun:
  face:
    accessKeyId: ${ALIBABA_CLOUD_ACCESS_KEY_ID:your_access_key_id}
    accessKeySecret: ${ALIBABA_CLOUD_ACCESS_KEY_SECRET:your_access_key_secret}
    endpoint: facebody.cn-shanghai.aliyuncs.com
    regionId: cn-shanghai

baseUrl: https://uat.rrskjfw.com.cn/
# 360额度
quotawsdl: TransEffectiveLimitFromCBSTo360.wsdl
quotalocation: classpath:TransEffectiveLimitFromCBSTo360.wsdl
# sap余额
sapwsdl: TransSupplierBalanceFromEVSToSAP.wsdl
saplocation: classpath:TransSupplierBalanceFromEVSToSAP.wsdl
# 金税
headwsdl: InsertLSMallZssHead.wsdl
headlocation: classpath:InsertLSMallZssHead.wsdl
itemwsdl: InsertLSMallZssItemNew.wsdl
itemlocation: classpath:InsertLSMallZssItemNew.wsdl
zsszfwsdl: InsertXwsqZssZF.wsdl
zsszflocation: classpath:InsertXwsqZssZF.wsdl
cacelwsdl: QueryCancelInterface.wsdl
cacelocation: classpath:QueryCancelInterface.wsdl
caceltable: qdtest.xwsq_zzs_zfhx
table: qdtest.xwsq_zzs_kphx
#ACS
acsurl: http://58.56.128.84:9001/12CTEST/EAI/RoutingProxyService/EAI_REST_POST_ServiceRoot?INT_CODE=EAI_INT_2552
#商户通
busicvp: http://cvp.hoptest.haier.net/services/busiCommon/Busi_Common_Srv?wsdl
hcspcvp: http://cvp.hoptest.haier.net/services/hcsp/HCSP_Sheet_Srv?wsdl
#海尔能力平台接口配置
hscapi:
  application:
    #系统编码
    key: 5f3e05613722cc046cfae6dbd3922743
    #系统秘钥
    secret: 5a00de01bebda4332d4bebccbfc6f6ca
  #票税云
  blade-seller-business:
    sellerinvoice:
      #票税云开票申请
      setXwsqZzsHeadVO:
        url: http://hscapi.haier.net/blade-seller-business/seller-apply-master/setXwsqZzsHeadVOCs
        methodName: setXwsqZzsHeadVO
      #票税云开票结果查询
      getSellerInvoice:
        url: http://hscapi.haier.net/blade-seller-business/sellerinvoice/getSellerInvoiceCs
        methodName: getSellerInvoice
      #税票云凭证归档
      setXwsqZzsArchive:
        url: http://hscapi.haier.net/blade-seller-business/sellerinvoice/setXwsqZzsArchiveCs
        methodName: setXwsqZzsArchive


finance:
  business:
    appId: news
    key: XA12#Da
    insertOrUpdateUrl: http://47.104.102.3:8001/finance/carMergerOrder/insertOrUpdate
    deleteOrderUrl: http://47.104.102.3:8001/finance/carMergerOrder/deleteOrder
    transferSubOrderUrl: http://47.104.102.3:8001/finance/details/transferSubOrder
    detailsEditUrl: http://47.104.102.3:8001/finance/details/edit
    invoiceEditUrl: http://47.104.102.3:8001/finance/headers/invoiceEdit
    searchOrderUrl: http://47.104.102.3:8001/finance/carMergerOrder/selectOrder
    transferApprovalFormUrl: http://47.104.102.3:8001/finance/approval/transferApprovalForm
    selectSettlementStatusUrl: http://47.104.102.3:8001/lfs/hcsp/selectSettlementStatus
    settlementCancelUrl: http://47.104.102.3:8001/lfs/hcsp/SettlementCancel
    newsDataAccessUrl: http://47.104.102.3:8001/lfs/hcsp/newsDataAccess
    settleAuditAddUrl: http://47.104.102.3:8001/settleAudit/add
    advAuditAddUrl: http://47.104.102.3:8001/lfs/advAudit/add
    lfsSettleAuditAddUrl: http://47.104.102.3:8001/lfs/settleAudit/add
    acAddUrl: http://47.104.102.3:8001/finance/ac/add
    newAdvanceSettlementUrl: http://47.104.102.3:8001/fncSettleAuditNew/newAdvanceSettlement
    callback:
      appId: finance
      appSecret: hrlLLGEaShHP6yE72he1l45T8jj7SVuU
storage:
  business:
    appId: storage
    secret: hZur4Kac
    leaveStoreUrl: http://uat.rrskjfw.com.cn/rrs-storage/smt/RRSGoodsLeaveOrder/leaveStoreIntf
    handleOutCompleteUrl: http://uat.rrskjfw.com.cn/rrs-storage/smt/RRSGoodsLeaveOrder/handleOutCompleteIntf
    handleInvalidUrl: http://uat.rrskjfw.com.cn/rrs-storage/smt/RRSGoodsLeaveOrder/handleInvalidIntf
byd:
  url: http://weixin109.bydauto.com.cn:18081
  AppSecret: k2hd83nsh4482jak261nsk3k382ha9g4
  APP_KEY: hd778h3h65k6978324nb2j1k3k7326hs
xk:
  url: https://xkdg.test.pc.rrskjfw.com.cn/prod-api
  AppSecret: k2hd83nsh4482jak261nsk3k382ha9g4
  APP_KEY: hd778h3h65k6978324nb2j1k3k7326hs

# 长安接口地址  测试域名前缀
cacfg:
  ACC_KEY: BU02J7E85SPSLBKYNQZ5
  SECRE_KEY: AUD4CDLAZZVB5YA1R6FEWFX45JTT7WN2
  SUPPLIER_CODE: *********
  baseUrl: https://cqtest.yonyouauto.com/gateway/api/
  #  正式域名前缀  https://dtd.changan.com.cn/gateway/api
  ##车企离线对接
bridgehub:
  jlUrl: https://sp-recharge.geely.com/InstallManage/orderDetail/
  url: http://************:8080/bridgehub/
  token: aaaaaa
  paramToken: 1053ce1c42e74370a77671dfe2d37cac

honeywell:
  # prod: https://dms-api.hopeful.company
  # test: http://hopeful-api.proxy.pinlor.cn
  baseUrl: http://hopeful-api.proxy.pinlor.cn
  client_id: dmj_system
  client_secret: x93hzuAXBR56Xfnv1
  grant_type: client_credentials password_sms
  scope: read

news:
  gace:
    app-id: DMJ
    app-secret: DMJ@202410150001123a
    base-url: https://srm-test.gace.com.cn:8022/ierp
    user: 12341111234
    id-number: HERRS
    page-size: 20
    company-id: 684
    query-hours: 2
    cancel-days: 1
    audit-days: 30
    field:
      woop-id: 1988
      asset-id: 1989
      woop-no: 1990
      act-finish-time: 1197
      act-check-time: 1062
      fault-tree-type: 2033
    tel: 400-9219898
    template-ids:
      - 713
      - 714
      - 715
      - 716
      - 717
    woop-wcp-id-number: DMJ
    repair-template-id: 378
    amap-param:
      key: 9d340f3139468618eb6819ff367d0f58
      url: https://restapi.amap.com/v3/geocode/geo
  lx:
    report-temp-path: /mnt2/file/
#    report-temp-path: /Users/<USER>/temp/

changcheng:
  brandId: 111
  persistentCcToken: W08Qnd1iiHzYucKiYvZLvYJI7ydkTfeb
  baseUrl: https://energy-jzdd-admin-test.gwm.com.cn
  supplierCode: 5777674c-1cdd-4557-8256-89b1788e1b26
  field:
    vinId: 2295 # 车架号
    mallOrderNoId: 2294 # 商城订单号
    dtcOrderNoId: 2296 # DTC 客户订单号
    endSubmitTimeId: 2312 # 安装权益到期时间
    carOwnerId: 2297 # 车主
    carOwnerPhoneId: 2298 # 车主联系方式
    installContactId: 2299 # 安装联系人
    installContactPhoneId: 2300 # 安装联系人电话
    orderRemarkId: 2301 # 订单备注字段ID
    dispatchTimeId: 2302 # 派单时间字段ID
    dispatchRemarkId: 2303 # 派单备注字段ID
    elecTypeId: 2304 # 用电类型字段ID
    checkRemarkId: 2313 # 勘测结果
    checkFinishTimeId: 2314 # 勘测完成时间
    hasPowerInstallId: 2305 # 是否具备安装条件
    noInstallConditionsId: 2315 # 不具备安装条件说明
    wireLengthId: 2307 # 布线距离
    installTypeId: 2309 # 铺设方式
    actualPriceId: 2308 # 客户支付费用
    pileModelNameId: 2310 # 充电桩型号
    installFinishTimeId: 2306 #安装完成时间
    pileCodeId: 2311 #充电桩编码

    personPilePhotoId: 1849 # 03: 人桩合影
    personPilePhotoId2: 1419 # 03: 人桩合影
    surveyAttachmentId: 1861  # 13: 勘测及其它附件
    surveyAttachmentId2: 1823  # 13: 勘测及其它附件
    surveyAttachmentId3: 1853  # 13: 长城--线路走向
    surveyAttachmentId4: 1854  # 13: 长城--工具摆放
    surveyAttachmentId5: 1855  # 13: 长城--模拟充电测试
    surveyAttachmentId6: 1856  # 13: 长城--电缆起止米标
    surveyAttachmentId7: 1857  # 13: 长城--电缆标识
    surveyAttachmentId8: 1858  # 13: 长城--绝缘电阻测试
    surveyAttachmentId9: 1859  # 13: 长城--接地电阻测试
    surveyAttachmentId10: 1860  # 13: 长城--增项项目施工照片
    additionalQuoteId: 1851  # 17: 增项报价单
    protectionSwitchId: 1852  # 18: 保护开关
    installationAcceptanceId: 1848  # 19: 安装竣工验收表
    chargingPileCodeId: 1421 # 20: 充电桩编号
    chargingPilePlateId: 1850 # 20: 充电桩铭牌
    powerPointId: 1862  # 22: 电源点
    cableMarkId: 1857 # 23: 线缆标识


leapmotor:
  appId: DMJfS4cM
  appSecret: Cu7rrlYIjc0MtcB27EWpew==
  baseUrl: https://psg-sitc.leapmotor.com
  field:
    vinId: 153 # 车架号
    orderNumber: 940 # 车企系统订单编号
    operationTimeCreate: 154 # 车企派单日期
    productOrderNumber: 2321 # 零跑-商品订单号
    customerName: 902 # 客户姓名
    customerPhone: 905 # 客户手机
    orderType: 2324 # 零跑-订单类型
    requiredProduct: 2325 # 零跑-需求产品
    requiredProductModel: 2326 # 零跑-需求产品型号
    submissionDate: 2327 # 零跑-提交日期
    carModel: 2328 # 零跑-车型
    packageType: 2329 # 零跑-套餐类型
    remark: 2330 # 零跑-备注-创建
    totalCableLength: 301 # 预计总布线长度
    installationMethod: 913 # 安装方式
    measuredVoltage: 295 # 实测电压
    surveyCompletionDate: 1196 # 实际勘测完成日期
    additionalChargeContent: 917 # 增项收费内容
    serviceStartTime: 2343 # 零跑-服务商首联时间
    estimatedUseProduct: 2342 # 零跑-预计使用产品
    parkingSpace: 2338 # 零跑-车位
    powerPoint: 2337 # 零跑-取电点
    installCondition: 2336 # 零跑-是否具备安装条件
    expectedPilingTime: 2335 # 零跑-预计收桩时间
    expectedDispatchTime: 2334 # 零跑-预计发桩时间
    bringPileOnSite: 2333 # 零跑-是否带桩上门
    noteSurvey: 2332 # 零跑-备注-勘测
    operationTimeSurvey: 2331 # 零跑-操作时间-勘测
    surveyReport: 263 # 勘测报告
    powerPointPhoto: 264 # 电源点照片
    parkingSpaceMap: 265 # 车位图
    other: 267 # 其他
    disclaimer: 2341 # 零跑-免责申明
    userNotification: 2340 # 零跑-用户告知书
    surveyForm: 2339 # 零跑-勘测表
    chargingPileCode: 1409 # 领跑充电桩编码
    installationMethodType: 1262 # 敷设方式
    groundResistance: 304 # 接地电阻
    chargingPileModel: 951 # 充电桩型号
    measuredVoltageValue: 927 # 实测电压值（L-N）
    parkingSpaceAddress: 936 # 车位地址
    powerSourceMethod: 920 # 取电方式
    installationMethodType2: 921 # 充电桩安装方式
    arrivalTime: 2359 # 零跑-实际上门时间
    serviceAppointmentTime: 2358 # 零跑-服务人员预约上门时间
    expectedVisitTime: 2357 # 零跑-客户期望上门时间
    installationConditionTime: 2356 # 零跑-客户具备安装条件时间
    grounding: 2354 # 零跑-是否接地
    actualProductUsed: 2348 # 零跑-实际使用产品
    extraAmountPaidOnSite: 2347 # 零跑-客户现场额外支付金额
    cableLengthUsed: 2346 # 零跑-实际使用线缆米数
    installationCompletionTime: 2345 # 零跑-实际安装完成时间
    installationOperationTime: 2344 # 零跑-操作时间-安装
    powerPointMap: 270 # 电源点图
    leakageBreakerDiagram: 271 # 漏电保护断路器图
    pileControlPhoto: 1534 # 零跑-绑桩控桩照片图
    personPilePhoto: 274 # 人桩合照（a规范着装，工服、安全帽、佩戴施工准入工作证；b充电桩指示灯正常状态）
    installationConfirmation: 269 # 安装确认书（内容完整、文字清晰、用户签字）
    cableLabelDiagram: 973 # 电缆线标识图  电缆标识（a品牌、型号清晰可见；b可明显分辨在相应安装现场）
    chargingPileLabel: 979 # 充电桩-编号   充电桩铭牌 （清晰完整，产品ID数字清晰可辨）
    testChargingDiagram: 966 # 模拟充电测试图--试充时零地电压（a 模拟器试充、车辆试充；b 万用表表盘数值清晰）
    cableMeterStartDiagram: 310 # 线缆米标图-起始 电缆首米标 （a可见电缆断头断尾；b分别以取电点和保护开关为背景；c米标清晰可辨）
    cableMeterEndDiagram: 278 # 线缆米标图-终点  电缆尾米标（a可见电缆断头断尾；b分别以取电点和保护开关为背景；c米标清晰可辨）
    installationCableRoute: 279 # 走线图-安装 （线路走向，标识清楚大体线路走向）
    additionalChargeQuote: 1017 # 增项报价单-安装
    surveyReportInstallation: 1261 # 勘测报告-安装
    installationVideo: 984 # 安装视频
    groundingPhoto: 2355 # 零跑-接地照片
    powerConnection: 2353 # 零跑-电源接线
    exceedStandardProject: 2352 # 零跑-超标工程
    distributionBox: 2351 # 零跑-配电箱
    acceptanceDocument: 2350 # 零跑-验收单据
    pileBody: 2349 # 零跑-桩体
    workOrderType: 1 # 工单类型
    carCompanyOrderNumber: 5 # 车企订单号
    carCompanyName: 101 # 车企名称
    workOrderSource: 306 # 工单来源
    customerName2: 902 # 客户姓名
    installationAddress: 903 # 安装地址
    customerEmail: 904 # 客户邮箱
    customerPhone2: 905 # 客户手机
    defaultSurveyForm: https://rrs-shbyd.oss-cn-shanghai.aliyuncs.com/20250915165540883_52195.jpg
    defaultUserNotice: https://rrs-shbyd.oss-cn-shanghai.aliyuncs.com/20250915165548130_96795.jpg
    defaultDisclaimer: https://rrs-shbyd.oss-cn-shanghai.aliyuncs.com/20250915165552643_85874.jpg

# API接口安全配置
api:
  security:
    # 是否启用接口安全认证
    enabled: true
    # 客户端密钥过期时间（小时）
    token-expire: 24
    # 客户端密钥配置
    clients:
      # 默认测试客户端
      test_client_id: 123456
      # 企业内部系统客户端 随机密钥
      warranty_card_id: 111111
