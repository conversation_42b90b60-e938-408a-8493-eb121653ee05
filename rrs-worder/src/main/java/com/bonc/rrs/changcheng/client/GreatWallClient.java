package com.bonc.rrs.changcheng.client;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdcardUtil;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bonc.rrs.changcheng.config.ChangChengConfig;
import com.bonc.rrs.changcheng.domain.*;
import com.bonc.rrs.changcheng.utils.CcUrlApi;
import com.bonc.rrs.changcheng.utils.SignUtil;
import com.bonc.rrs.intf.service.IntfLogService;
import com.bonc.rrs.util.FileUtils;
import com.bonc.rrs.util.SmsUtil;
import com.bonc.rrs.worder.dao.WorderInformationAttributeDao;
import com.bonc.rrs.worder.entity.*;
import com.bonc.rrs.worder.service.BizAttendantService;
import com.bonc.rrs.worder.service.ExtFieldService;
import com.bonc.rrs.worder.service.WorderExtFieldService;
import com.bonc.rrs.worder.service.WorderInformationService;
import com.bonc.rrs.workManager.entity.SysFileEntity;
import com.bonc.rrs.workManager.service.SysFilesService;
import com.common.pay.common.utils.DateUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.youngking.lenmoncore.common.utils.RedisUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class GreatWallClient {

    @Value("${changcheng.baseUrl:https://energy-jzdd-admin-test.gwm.com.cn}")
    private String baseUrl;
    @Value("${changcheng.supplierCode:5777674c-1cdd-4557-8256-89b1788e1b26}")
    private String supplierCode;

    private final RestTemplate restTemplate;

    private final IntfLogService intfLogService;

    private final WorderInformationAttributeDao worderInformationAttributeDao;


    private final BizAttendantService bizAttendantService;

    private final WorderInformationService worderInformationService;

    private final ExtFieldService extFieldService;

    private final SysFilesService sysFilesService;

    private final ChangChengConfig changChengConfig;

    private final WorderExtFieldService worderExtFieldService;

    private final RedisUtils redisUtils;

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern(DateUtil.LONG_DATE_FORMAT);
    private static final DateTimeFormatter DATE_FORMAT = DateTimeFormatter.ofPattern(DateUtil.FORMAT_ONE);
    /**
     * 推送安装订单到长城 SubmitInstallOrderRequest
     */
    public void pushInstallOrder2(SubmitInstallOrderRequest request) {

            request.setSupplierCode(request.getSupplierCode() == null ? supplierCode : request.getSupplierCode());
            request.setSign(SignUtil.generateSign(request.getSupplierCode()));
            pushData(CcUrlApi.PUSH_INSTALL_ORDER, request, request.getOrderNo());

    }
    /**
     * 推送首联信息到长城 pushFirstContactInstall
     */
    public void pushFirstContactInstall(Integer worderId, String companyOrderNumber, String worderNo, String userPhone) {
        // 防重复提交校
        String fileKey = "push_first_contact_lock:" + companyOrderNumber;

        // 尝试设置分布式锁
        boolean lockAcquired = redisUtils.setIfAbsent(fileKey, "pushFirstContact", 5);
        if (!lockAcquired) {
            return;
        }

        try {
            List<WorderExtFieldEntity> fieldEntityList = worderExtFieldService.getFieldsByWorderNo(worderNo);
            if (CollUtil.isEmpty(fieldEntityList)) {
                log.error("推送安装订单到长城失败，工单编号：{}，未能查询到安装信息", worderNo);
                return;
            }
            //获取到手机号，发送短信
            SmsUtil.sendSms(userPhone, "尊敬的车主您好！您的充电桩安装需求已接收，24小时内客服人员会联系您，如有其他需求可以致电4009219898联系我们。", "【到每家科技服务】");
            String firstCallTime = LocalDateTime.now().format(DATE_FORMAT);
            SubmitInstallOrderRequest request = new SubmitInstallOrderRequest();
            request.setOrderNo(companyOrderNumber);
            request.setContactCustTime(parseDate(firstCallTime));
            request.setSaveOrSubmit("01");
            QueryWrapper<WorderInformationAttributeEntity> wrapper = new QueryWrapper<>();
            wrapper.eq("worder_id", worderId);
            wrapper.eq("attribute_code", "changChengFirstContact");
            wrapper.last("limit 1");
            WorderInformationAttributeEntity attribute = worderInformationAttributeDao.selectOne(wrapper);
            if (Objects.isNull(attribute)) {
                worderInformationAttributeDao.insertCcCancelOrder(
                        worderId, "changChengFirstContact",
                        "长城触发首次联系客户时间", firstCallTime, "");
            }
            request.setSupplierCode(request.getSupplierCode() == null ? supplierCode : request.getSupplierCode());
            request.setSign(SignUtil.generateSign(request.getSupplierCode()));
            pushData(CcUrlApi.PUSH_INSTALL_ORDER, request, request.getOrderNo());



        } catch (Exception e) {
            log.error("推送安装订单到长城失败，工单编号：{}，异常信息：{}", worderNo, e.getMessage());
        } finally {
            // 确保释放分布式锁
            try {
                redisUtils.remove(fileKey);
            } catch (Exception e) {
                // 记录日志但不影响主流程
                System.err.println("释放推送首联信息到长城锁失败: " + e.getMessage());
            }
        }

    }
    /**
     * 推送安装订单到长城 SubmitInstallOrderRequest
     */
    public void pushInstallOrderInstall(SubmitInstallOrderRequest request, String worderNo, Date createTime) {
        if (nonCcOrder(worderNo)) {
            return;
        }
        if (Objects.equals(request.getHasPowerInstall(), "N")) {
            request.setNextContactTime(adjustToBusinessDay(createTime));
        }
        request.setSupplierCode(request.getSupplierCode() == null ? supplierCode : request.getSupplierCode());
        request.setSign(SignUtil.generateSign(request.getSupplierCode()));
        pushData(CcUrlApi.PUSH_INSTALL_ORDER, request, request.getOrderNo());
    }

    /**
     * 推送安装订单到长城 SubmitInstallOrderRequest
     */
    public void pushInstallOrder(WorderInformationEntity informationEntity, String firstCallTime) {
        if (nonCcOrder(informationEntity.getWorderNo())) {
            return;
        }
        try {
            SubmitInstallOrderRequest request = new SubmitInstallOrderRequest();
            request.setOrderNo(informationEntity.getCompanyOrderNumber());
            //首联
            if (StringUtils.isNotEmpty(firstCallTime)) {
                request.setSaveOrSubmit("01");
                request.setContactCustTime(parseDate(firstCallTime));
                QueryWrapper<WorderInformationAttributeEntity> wrapper = new QueryWrapper<>();
                wrapper.eq("worder_id", informationEntity.getWorderId());
                wrapper.eq("attribute_code", "changChengFirstContact");
                wrapper.last("limit 1");
                WorderInformationAttributeEntity attribute = worderInformationAttributeDao.selectOne(wrapper);
                //如果已存在首联时间，则不推送，直接返回
                if (Objects.nonNull(attribute)) {
                    return;
                }
                worderInformationAttributeDao.insertCcCancelOrder(
                        informationEntity.getWorderId(), "changChengFirstContact",
                        "长城触发首次联系客户时间", firstCallTime, "");
                //安装完成/服务商内审完成提交必填项或信息同步
            } else {

                List<WorderExtFieldEntity> entityList = informationEntity.getWorderExtFieldList();
                //发送附件
                pushFileOrder(informationEntity.getCompanyOrderNumber(), entityList);

                Map<Integer, String> fieldMap = entityList.stream()
                        .filter(e -> e.getFieldValue() != null)
                        .collect(Collectors.toMap(
                                WorderExtFieldEntity::getFieldId,
                                WorderExtFieldEntity::getFieldValue,
                                (t1, t2) -> t1));

                ChangChengConfig.ChangChengField field = changChengConfig.getField();

                QueryWrapper<BizAttendantEntity> wrapper = new QueryWrapper<>();
                wrapper.eq("id", informationEntity.getServiceId());
                BizAttendantEntity bizAttendant = bizAttendantService.getOne(wrapper);
                WorderInfoEntity worderInfoEntity = worderInformationService.getByWorderId(informationEntity.getWorderId());
                // 操作类型设置
                // 01保存/02提交
                request.setSaveOrSubmit("02");
                LocalDateTime now = LocalDateTime.now();

                // 日期字段设置（按照图片中的字段名对应数据库字段）接单时间<首联时间<勘测时间<=安装时间
                String dispatchTime = fieldMap.getOrDefault(field.getDispatchTimeId(), DateUtil.dateToString(new Date(), DateUtil.FORMAT_ONE));


                // 勘测完成时间
                LocalDateTime checkFinishTime = fieldMap.containsKey(field.getCheckFinishTimeId()) ?
                        completeDateTime(fieldMap.get(field.getCheckFinishTimeId())) : now;
                // 安装完成时间
                LocalDateTime installFinishTime = fieldMap.containsKey(field.getInstallFinishTimeId()) ?
                        completeDateTime(fieldMap.get(field.getInstallFinishTimeId())) : now;
                // 预约安装时间（必填）
                Date installAppointTime = parseDate(worderInfoEntity.getInstallAppointTime());

                request.setAppointmentInstallTime(installAppointTime);

                // 预约勘测联系客户时间（首联时间）（必填）
                QueryWrapper<WorderInformationAttributeEntity> firstWrapper = new QueryWrapper<>();
                firstWrapper.eq("worder_id", informationEntity.getWorderId());
                firstWrapper.eq("attribute_code", "changChengFirstContact");
                firstWrapper.last("limit 1");
                WorderInformationAttributeEntity attribute = worderInformationAttributeDao.selectOne(firstWrapper);
                if (Objects.nonNull(attribute)) {
                    worderInfoEntity.setFirstCallTime(parseDate(attribute.getAttributeValue()));
                }
                Date firstCallTime2 = worderInfoEntity.getFirstCallTime() == null ? adjustTimeWithRandomMinutes(informationEntity.getCreateTime()) : worderInfoEntity.getFirstCallTime();
                request.setContactCustTime(firstCallTime2);

                // 下次联系时间（非必填）
                request.setNextContactTime(parseDate(worderInfoEntity.getNextContactTime()));
                // 预约勘测时间（必填）
                request.setAppointmentTime(parseDate(worderInfoEntity.getConveyAppointTime()));
                // 勘测完成时间（必填） 新加的扩展表
                request.setCheckFinishTime(convertToDate(checkFinishTime));
                // 具备安装条件时间（必填）
                request.setPowerInstallFinishTime(parseDate(worderInfoEntity.getInstallAppointTime()));
                // 预约安装联系客户时间（必填）首联后 + 6H
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(request.getContactCustTime());
                calendar.add(Calendar.HOUR_OF_DAY, 6);
                request.setInstallContactCustTime(calendar.getTime());
                // 安装完成时间（必填） 扩展字段-实际安装完成时间
                request.setInstallFinishTime(convertToDate(installFinishTime));

                // 字符串字段设置
                // 预约勘测备注（非必填）
                request.setAppointmentRemark(null);
                // 派单勘测备注（非必填）
                request.setAssignCheckRemark(null);
                // 勘测电工（必填）
                request.setCheckUserName(bizAttendant.getName());
                // 勘测电工联系方式（必填）
                request.setCheckUserPhone(bizAttendant.getContact());
                // 用电类型（必填） 新加的扩展表
                String elecTypeId = fieldMap.getOrDefault(field.getElecTypeId(), "自家电");
                request.setElecType(getElectricityTypeCode(elecTypeId));
                // 预计收费金额（必填） 新加的扩展表
                String actualPriceId = fieldMap.getOrDefault(field.getActualPriceId(), "0");
                request.setEstimatePrice(actualPriceId);
                // 勘测结果（必填） 新加的扩展表
                request.setCheckRemark(fieldMap.getOrDefault(field.getCheckRemarkId(), "完成"));
                // 是否具备安装条件（必填） 新加的扩展表
//                request.setHasPowerInstall(fieldMap.getOrDefault(field.getHasPowerInstallId(), "Y"));
                request.setHasPowerInstall("Y");
                // 不具备安装条件说明（非必填） 新加的扩展表
                request.setNoInstallConditionsRemark(fieldMap.getOrDefault(field.getNoInstallConditionsId(), "无"));
                // 派单安装备注（非必填）
                request.setAssignInstallRemark(null);
                // 安装电工（必填）
                request.setInstallUserName(bizAttendant.getName());
                // 安装电工联系方式（必填）
                request.setInstallUserPhone(bizAttendant.getContact());
                // 布线距离（必填） 新加的扩展表
                request.setWireLength(fieldMap.getOrDefault(field.getWireLengthId(), "10"));
                // 铺设方式（必填） 新加的扩展表
                String installTypeId = fieldMap.getOrDefault(field.getInstallTypeId(), "其他");
                request.setInstallType(getLayingMethodCode(installTypeId));
                // 经度（非必填）
                request.setGpsLon(null);
                // 纬度（非必填）
                request.setGpsLat(null);
                // 客户支付费用（必填） 新加的扩展表
                request.setActualPrice(actualPriceId);
                // 安装备注（非必填）
                request.setInstallRemark(worderInfoEntity.getCandidateAttendant());
                // 充电桩编码（必填）
                request.setPileCode(fieldMap.getOrDefault(field.getPileCodeId(), RandomUtil.simpleUUID()));
                adjustTimeSequence(dispatchTime, firstCallTime2, installAppointTime, checkFinishTime, installFinishTime, request);
            }

            request.setSupplierCode(request.getSupplierCode() == null ? supplierCode : request.getSupplierCode());
            request.setSign(SignUtil.generateSign(request.getSupplierCode()));
            pushData(CcUrlApi.PUSH_INSTALL_ORDER, request, request.getOrderNo());

        } catch (Exception e) {
            log.error("推送安装订单到长城异常", e);
            throw e;
        }
    }

    /**
     * 推送邮寄订单到长城
     */
    public void pushDeliveryOrder(PushDeliveryOrderRequest request) {
        request.setSupplierCode(request.getSupplierCode() == null ? supplierCode : request.getSupplierCode());
        request.setSign(SignUtil.generateSign(request.getSupplierCode()));
        pushData(CcUrlApi.PUSH_DELIVERY_ORDER, request, request.getOrderNo());
    }

    /**
     * 订单附件同步至长城
     */
    public void pushFileOrder(String orderNo, List<WorderExtFieldEntity> fieldList) {
        PushFileRequest request = new PushFileRequest();
        request.setOrderNo(orderNo);
        List<PushFileRequest.AttachInfo> attachList = new ArrayList<>();

        // 1. 创建配置字段映射表（附件类型 -> 关联的字段ID集合）
        Map<String, Set<Integer>> attachTypeFieldMap = new LinkedHashMap<>();
        ChangChengConfig.ChangChengField fieldConfig = changChengConfig.getField();
        attachTypeFieldMap.put("03", Sets.newHashSet(fieldConfig.getPersonPilePhotoId(), fieldConfig.getPersonPilePhotoId2()));
        attachTypeFieldMap.put("13", Sets.newHashSet(fieldConfig.getSurveyAttachmentId(), fieldConfig.getSurveyAttachmentId2(),
                fieldConfig.getSurveyAttachmentId3(), fieldConfig.getSurveyAttachmentId4(), fieldConfig.getSurveyAttachmentId5(),
                fieldConfig.getSurveyAttachmentId6(), fieldConfig.getSurveyAttachmentId7(), fieldConfig.getSurveyAttachmentId8(),
                fieldConfig.getSurveyAttachmentId9(), fieldConfig.getSurveyAttachmentId10()));
        attachTypeFieldMap.put("17", Sets.newHashSet(fieldConfig.getAdditionalQuoteId()));
        attachTypeFieldMap.put("18", Sets.newHashSet(fieldConfig.getProtectionSwitchId()));
        attachTypeFieldMap.put("19", Sets.newHashSet(fieldConfig.getInstallationAcceptanceId()));
        attachTypeFieldMap.put("20", Sets.newHashSet(fieldConfig.getChargingPileCodeId(), fieldConfig.getChargingPilePlateId()));
        // 合并相同附件类型
        attachTypeFieldMap.put("22", Sets.newHashSet(fieldConfig.getPowerPointId()));
        attachTypeFieldMap.put("23", Sets.newHashSet(fieldConfig.getCableMarkId()));

        // 2. 预加载扩展字段配置
        Map<Integer, ExtFieldEntity> extFieldMap = extFieldService.listByIds(
                fieldList.stream().map(WorderExtFieldEntity::getFieldId).collect(Collectors.toSet())
        ).stream().collect(Collectors.toMap(ExtFieldEntity::getFieldId, Function.identity()));

        // 3. 收集需要处理的文件ID（直接使用Set避免拼接字符串）
        Map<Integer, List<String>> fieldFileMap = new LinkedHashMap<>();
        Set<String> allFileIds = new HashSet<>();

        for (WorderExtFieldEntity entity : fieldList) {
            ExtFieldEntity extField = extFieldMap.get(entity.getFieldId());
            if (isValidAttachmentField(extField, entity)) {
                List<String> fileIds = Arrays.asList(entity.getFieldValue().split(","));
                fieldFileMap.put(entity.getFieldId(), fileIds);
                allFileIds.addAll(fileIds);
            }
        }

        // 4. 批量查询文件信息
        if (!allFileIds.isEmpty()) {
            Map<String, SysFileEntity> fileMap = sysFilesService.getSysFileByIds(
                    String.join(",", allFileIds)
            ).stream().collect(Collectors.toMap(
                    file -> file.getFileId().toString(),
                    Function.identity()
            ));

            // 5. 构建附件信息（使用映射表替代多个if分支）
            fieldFileMap.forEach((fieldId, fileIds) -> {
                attachTypeFieldMap.forEach((attachType, configFieldIds) -> {
                    if (configFieldIds.contains(fieldId)) {
                        fileIds.stream()
                                .map(fileMap::get)
                                .filter(Objects::nonNull)
                                .forEach(file -> attachList.add(
                                        new PushFileRequest.AttachInfo(attachType, FileUtils.copyImage(file.getNewName()))
                                ));
                    }
                });
            });
        }

        // 6. 设置请求参数
        request.setAttachList(attachList);
        String finalSupplierCode = Optional.ofNullable(request.getSupplierCode()).orElse(supplierCode);
        request.setSupplierCode(finalSupplierCode);
        request.setSign(SignUtil.generateSign(finalSupplierCode));

        // 7. 发送数据
        pushData(CcUrlApi.PUSH_FILE, request, orderNo);
    }

    /** 校验是否为有效的附件字段 */
    private boolean isValidAttachmentField(ExtFieldEntity extField, WorderExtFieldEntity entity) {
        return extField != null
                && extField.getFieldType() == 3
                && extField.getFieldPurpose() == 3
                && StringUtils.isNotBlank(entity.getFieldValue());
    }


    /**
     * 订单附件同步至长城
     */
    public void pushFileOrder2(PushFileRequest request) {
        request.setSupplierCode(request.getSupplierCode() == null ? supplierCode : request.getSupplierCode());
        request.setSign(SignUtil.generateSign(request.getSupplierCode()));
        pushData(CcUrlApi.PUSH_FILE, request, request.getOrderNo());
    }

    /**
     * 安装（邮寄）订单初审流程同步至长城
     */
    public String pushSubmitCancelOrder(Integer worderId, String orderNo, String worderNo, String auditType, String auditRemark) {
        if (nonCcOrder(worderNo)) {
            return null;
        }
            try {
                SubmitCancelRequest request = new SubmitCancelRequest();
                request.setOrderNo(orderNo);
                request.setFirstCheckFlag(auditType);
                request.setFirstCheckRemark(auditRemark);
                //获取作废申请单号
                QueryWrapper<WorderInformationAttributeEntity> wrapper = new QueryWrapper<>();
                wrapper.eq("worder_id", worderId);
                wrapper.eq("attribute_code", "cc_cancel_order_no");
                wrapper.last("limit 1");
                WorderInformationAttributeEntity attribute = worderInformationAttributeDao.selectOne(wrapper);
                request.setCancelOrderNo(Objects.nonNull(attribute) ? attribute.getAttributeValue() : null);
                request.setSupplierCode(request.getSupplierCode() == null ? supplierCode : request.getSupplierCode());
                request.setSign(SignUtil.generateSign(request.getSupplierCode()));
                pushData(CcUrlApi.PUSH_CANCEL, request, request.getOrderNo());
            }catch (Exception e) {
                log.error("长城 安装（邮寄）订单初审流程同步至长城异常", e);
                return e.getMessage();
            }
            return null;
    }

    /**
     * 安装（邮寄）订单催单处理结果同步长城
     */
    public String pushSubmitReminderOrder(SubmitReminderRequest request, String worderNo) {
        if (nonCcOrder(worderNo)) {
            return null;
        }
        request.setOrderType("01");

        request.setSupplierCode(request.getSupplierCode() == null ? supplierCode : request.getSupplierCode());
        request.setSign(SignUtil.generateSign(request.getSupplierCode()));
        pushData(CcUrlApi.PUSH_REMINDER, request, request.getOrderNo());
        return null;
    }





    /**
     * 统一推送方法
     */
    private void pushData(CcUrlApi url, Object request, String orderCode) {
        String baseUtl = baseUrl + url.getCode();
        try {

            // 1. 添加认证头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            // 2. 构建请求
            HttpEntity<Object> httpEntity = new HttpEntity<>(request, headers);

            log.info("长城推送基本信息接口：{}，入参：{}", url.getCode(), JSON.toJSONString(request));
            // 3. 发送请求
            ResponseEntity<String> response = restTemplate.exchange(
                    baseUtl,
                    HttpMethod.POST,
                    httpEntity,
                    String.class);
            log.info("长城推送基本信息接口：{}，出参：{}", url.getCode(), JSON.toJSONString(response));


            intfLogService.saveIntfLog(url.getCode(), orderCode, url.getCode(), url.getDesc(), 3, JSON.toJSONString(request), JSON.toJSONString(response));



            // 4. 处理响应
            if (!response.getStatusCode().is2xxSuccessful()) {
                log.error("推送数据到长城失败，URL: {}, 状态码: {}, 响应: {}",
                        url, response.getStatusCode(), response.getBody());
                throw new RuntimeException("长城接口返回非成功状态码");
            }

            log.info("成功推送数据到长城，URL: {}", url);

        } catch (Exception e) {
            log.error("推送数据到长城异常，URL: {}", url, e);
            throw new RuntimeException("推送数据到长城异常", e);
        }
    }


    private boolean nonCcOrder(String worderNo) {
        WorderInformationAttributeEntity pushOrder =
                worderInformationAttributeDao.selectAttributeByWorderNoAttVal(worderNo, "worder_source", "pushOrder", "changcheng");
        return Objects.isNull(pushOrder);
    }


    public Date parseDate(String date) {
        if (com.youngking.lenmoncore.common.utils.StringUtils.isBlank(date)) {
            return null;
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            return simpleDateFormat.parse(date);
        } catch (ParseException e) {
            return null;
        }
    }

    /**
     * 将传入的日期补全为当天的时分秒，但不晚于当前时间
     * @param dateStr 日期字符串，格式为"yyyy-MM-dd"
     * @return 补全后的LocalDateTime对象
     */

    public static LocalDateTime completeDateTime(String dateStr) {
        try {
            // 先尝试解析带时间的格式 (yyyy-MM-dd HH:mm:ss)
            return LocalDateTime.parse(dateStr, DATE_FORMAT);
        } catch (DateTimeParseException e1) {
            try {
                // 如果失败，尝试解析仅日期的格式 (yyyy-MM-dd)
                LocalDate date = LocalDate.parse(dateStr, DATE_FORMATTER);
                LocalDateTime now = LocalDateTime.now();

                if (date.isEqual(now.toLocalDate())) {
                    // 如果是今天，使用当前时间减去1小时
                    return now.minusHours(1L);
                } else if (date.isBefore(now.toLocalDate())) {
                    // 如果是过去日期，使用当天的23:59:59
                    return LocalDateTime.of(date, LocalTime.MAX);
                } else {
                    // 如果是未来日期，使用当天的00:00:00
                    return LocalDateTime.of(date, LocalTime.MIN);
                }
            } catch (DateTimeParseException e2) {
                return LocalDateTime.now();
            }
        }
    }


    /**
     * 处理勘测和安装日期时间
     * @param surveyDateStr 勘测日期字符串
     * @param installDateStr 安装日期字符串
     * @return 包含处理后的勘测和安装日期时间的对象
     */
    public static LocalDateTime processSurveyAndInstallDates(String surveyDateStr, String installDateStr) {
        // 解析日期
        LocalDateTime surveyDateTime = LocalDateTime.parse(surveyDateStr, DATE_FORMAT);
        LocalDateTime installDateTime = LocalDateTime.parse(installDateStr, DATE_FORMAT);

        // 处理勘测日期时间

        if (surveyDateTime.isEqual(installDateTime)) {
            // 日期相同，勘测时间设为安装时间6小时后
            surveyDateTime = installDateTime.plusMinutes(1);

            // 如果6小时后超过了当天23:59:59，则设置为23:59:59
            if (surveyDateTime.isAfter(installDateTime)) {
                surveyDateTime = LocalDateTime.of(installDateTime.toLocalDate(), LocalTime.MAX);
            }
        } else if (surveyDateTime.isAfter(installDateTime)) {
            // 日期相同，勘测时间设为安装时间6小时后
            surveyDateTime = installDateTime.plusMinutes(1);
        }

        return surveyDateTime;
    }

    /**
     * 将LocalDateTime转换为Date
     * @param localDateTime 要转换的LocalDateTime对象
     * @return 对应的Date对象
     */
    public static Date convertToDate(LocalDateTime localDateTime) {
        if (localDateTime == null) {
            return null;
        }
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }


    public static String getElectricityTypeCode(String name) {
        if (name == null) {
            return "02";
        }

        switch (name) {
            case "物业电":
                return "01";
            case "国网电":
                return "03";
            case "自家电":
            default:
                return "02";
        }
    }

    public static String getLayingMethodCode(String name) {
        if (name == null) {
            return "05";
        }

        switch (name) {
            case "穿管":
                return "01";
            case "桥架":
                return "02";
            case "地沟":
                return "03";
            case "暗敷":
                return "04";
            case "其他":
            default:
                return "05";
        }
    }
    /**
     * 调整时间顺序
     * @param dispatchTime 派单时间
     * @param firstCallTime 首联时间
     * @param installAppointTime 安装预约时间
     * @param checkFinishTime 勘测完成时间
     * @param installFinishTime 安装完成时间
     * @param request 提交安装订单请求对象
     */
    public static void adjustTimeSequence(String dispatchTime,
                                          Date firstCallTime,
                                          Date installAppointTime,
                                          LocalDateTime checkFinishTime,
                                          LocalDateTime installFinishTime,
                                          SubmitInstallOrderRequest request) {

        // 转换派单时间为LocalDateTime
        LocalDateTime dispatchDateTime = LocalDateTime.parse(dispatchTime, DATE_FORMAT);

        // 处理首联时间
        LocalDateTime firstCallDateTime = null;
        if (firstCallTime != null) {
            firstCallDateTime = convertDateToLocalDateTime(firstCallTime);

            // 确保派单时间 < 首联时间
            if (!dispatchDateTime.isBefore(firstCallDateTime)) {
                firstCallDateTime = dispatchDateTime.plusMinutes(1);
                firstCallTime.setTime(convertLocalDateTimeToDate(firstCallDateTime).getTime());
            }
        } else if (installAppointTime != null) {
            // 如果没有首联时间，但存在预约安装时间，则用预约时间作为首联时间
            firstCallDateTime = convertDateToLocalDateTime(installAppointTime);

            // 确保派单时间 < 预约时间（此时预约时间 = 首联时间）
            if (!dispatchDateTime.isBefore(firstCallDateTime)) {
                firstCallDateTime = dispatchDateTime.plusMinutes(1);
            }
            firstCallTime = convertLocalDateTimeToDate(firstCallDateTime);
        }
        // 3. 处理预约安装时间（installAppointTime）
        if (installAppointTime != null) {
            LocalDateTime installAppointDateTime = convertDateToLocalDateTime(installAppointTime);

            // 如果首联时间存在，预约时间必须 ≥ 首联时间
            if (firstCallDateTime != null && installAppointDateTime.isBefore(firstCallDateTime)) {
                installAppointDateTime = firstCallDateTime;
            }
            // 如果首联时间不存在，预约时间必须 > 派单时间
            else if (firstCallDateTime == null && !dispatchDateTime.isBefore(installAppointDateTime)) {
                installAppointDateTime = dispatchDateTime.plusMinutes(1);
            }
            // 这里可以返回调整后的 installAppointTime，但由于 String 不可变，需要外部处理
            installAppointTime = convertLocalDateTimeToDate(installAppointDateTime);
        }

        // 如果有勘测完成时间，确保首联时间 < 勘测完成时间
        if (checkFinishTime != null && firstCallDateTime != null) {
            if (!firstCallDateTime.isBefore(checkFinishTime)) {
                checkFinishTime = firstCallDateTime.plusMinutes(1);
            }
        }

        // 确保勘测完成时间 <= 安装时间
        if (checkFinishTime != null && installFinishTime != null) {
            if (checkFinishTime.isAfter(installFinishTime)) {
                installFinishTime = checkFinishTime;
            }
        }

        // 最后再次确保派单时间 < 首联时间 < 勘测完成时间 <= 安装时间
        if (firstCallDateTime != null && checkFinishTime != null) {
            if (!dispatchDateTime.isBefore(firstCallDateTime)) {
                firstCallDateTime = dispatchDateTime.plusMinutes(1);
                firstCallTime.setTime(convertLocalDateTimeToDate(firstCallDateTime).getTime());
            }

            if (!firstCallDateTime.isBefore(checkFinishTime)) {
                checkFinishTime = firstCallDateTime.plusMinutes(1);
            }
        }

        request.setAppointmentInstallTime(installAppointTime);
        request.setContactCustTime(firstCallTime);
        request.setCheckFinishTime(convertLocalDateTimeToDate(checkFinishTime));
        request.setInstallFinishTime(convertLocalDateTimeToDate(installFinishTime));
        request.setAppointmentTime(installAppointTime);
    }

    private static LocalDateTime convertDateToLocalDateTime(Date date) {
        return date.toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDateTime();
    }

    private static Date convertLocalDateTimeToDate(LocalDateTime localDateTime) {
        return Date.from(localDateTime.atZone(java.time.ZoneId.systemDefault()).toInstant());
    }


    /**
     *往后加2天，并且在工作日内的早上九点
     * @param dateTimeStr  
 * @return Date
     */
     
    public static Date adjustToBusinessDay(Date dateTimeStr) {
        // 解析输入字符串为LocalDateTime
        LocalDateTime dateTime = convertDateToLocalDateTime(dateTimeStr);

        // 添加2天
        LocalDateTime plusTwoDays = dateTime.plusDays(2);

        // 调整到下一个工作日（跳过周末）
        LocalDateTime businessDay = plusTwoDays;
        switch (plusTwoDays.getDayOfWeek()) {
            case SATURDAY:
                businessDay = plusTwoDays.plusDays(2); // 周六+2天=周一
                break;
            case SUNDAY:
                businessDay = plusTwoDays.plusDays(1); // 周日+1天=周一
                break;
            default:
                // 工作日无需调整日期
                break;
        }

        // 设置时间为早上9点
        LocalDateTime result = businessDay.withHour(9)
                .withMinute(0)
                .withSecond(0)
                .withNano(0);

        return convertLocalDateTimeToDate(result);
    }


/**
 *接单时间8：00-20：00的工单，首联时间为接单时间+3-5分钟内动态时间，接单为昨天20：00-当天7：59的，首联时间为接单时间+15-25分钟内动态时间
 * @param dateTimeStr  接单时间
 * @return Date
 */
    public static Date adjustTimeWithRandomMinutes(Date dateTimeStr) {
        Random random = new Random();
        // 解析输入字符串为LocalDateTime
        LocalDateTime dateTime = convertDateToLocalDateTime(dateTimeStr);

        // 获取小时和分钟
        int hour = dateTime.getHour();
        int minute = dateTime.getMinute();

        // 根据时间段确定随机分钟范围
        int minMinutes, maxMinutes;
        if (hour >= 8 && hour < 20) {
            // 8:00-19:59 时间段
            minMinutes = 3;
            maxMinutes = 5;
        } else {
            // 20:00-次日7:59 时间段
            minMinutes = 15;
            maxMinutes = 25;
        }

        // 生成随机分钟数
        int randomMinutes = minMinutes + random.nextInt(maxMinutes - minMinutes + 1);

        // 添加随机分钟数
        LocalDateTime adjustedDateTime = dateTime.plusMinutes(randomMinutes);

        return convertLocalDateTimeToDate(adjustedDateTime);
    }



}
