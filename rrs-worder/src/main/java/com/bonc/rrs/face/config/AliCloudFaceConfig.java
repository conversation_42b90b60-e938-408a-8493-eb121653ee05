package com.bonc.rrs.face.config;

import com.aliyun.facebody20191230.Client;
import com.aliyun.teaopenapi.models.Config;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 阿里云人脸比对配置类
 * <AUTHOR>
 */
@Configuration
@ConfigurationProperties(prefix = "aliyun.face")
@Data
@Slf4j
public class AliCloudFaceConfig {

    /**
     * 阿里云AccessKeyId
     */
    private String accessKeyId;

    /**
     * 阿里云AccessKeySecret
     */
    private String accessKeySecret;

    /**
     * 阿里云人脸比对服务端点
     */
    private String endpoint;

    /**
     * 阿里云区域ID
     */
    private String regionId;

    /**
     * 创建阿里云人脸比对客户端
     * @return 人脸比对客户端
     */
    @Bean
    public Client createFaceClient() {
        try {
            Config config = new Config()
                    .setAccessKeyId(accessKeyId)
                    .setAccessKeySecret(accessKeySecret);
            config.endpoint = endpoint;
            
            log.info("初始化阿里云人脸比对客户端成功，endpoint: {}", endpoint);
            return new Client(config);
        } catch (Exception e) {
            log.error("初始化阿里云人脸比对客户端失败", e);
            throw new RuntimeException("初始化阿里云人脸比对客户端失败", e);
        }
    }
}
