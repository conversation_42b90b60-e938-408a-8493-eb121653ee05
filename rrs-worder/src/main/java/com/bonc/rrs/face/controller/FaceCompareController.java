package com.bonc.rrs.face.controller;

import com.bonc.rrs.face.dto.FaceCompareRequestDto;
import com.bonc.rrs.face.dto.FaceCompareResponseDto;
import com.bonc.rrs.face.service.FaceCompareService;
import com.youngking.lenmoncore.common.utils.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 人脸比对控制器
 * <AUTHOR>
 */
@RestController
@RequestMapping("/app/face")
@Api(tags = {"人脸比对接口"})
@Slf4j
public class FaceCompareController {

    @Autowired
    private FaceCompareService faceCompareService;

    /**
     * 人脸比对接口
     * @param request 比对请求参数
     * @return 比对结果
     */
    @PostMapping("/compare")
    @ApiOperation("人脸比对")
    public R compareFace(@RequestBody @Validated FaceCompareRequestDto request) {
        try {
            log.info("收到人脸比对请求，照片URL：{}", request.getFaceImageUrl());
            
            FaceCompareResponseDto response = faceCompareService.compareFace(request);
            
            if ("200".equals(response.getStatusCode())) {
                log.info("人脸比对成功，结果：{}，相似度：{}", response.getIsSamePerson(), response.getSimilarity());
                return R.ok().put("data", response);
            } else {
                log.warn("人脸比对失败，错误码：{}，错误信息：{}", response.getStatusCode(), response.getStatusMessage());
                return R.error(Integer.parseInt(response.getStatusCode()), response.getStatusMessage()).put("data", response);
            }
        } catch (Exception e) {
            log.error("人脸比对接口异常", e);
            return R.error("人脸比对接口异常：" + e.getMessage());
        }
    }

    /**
     * 获取当前服务兵身份证照片URL（用于测试）
     * @return 身份证照片URL
     */
    @GetMapping("/idcard-url")
    @ApiOperation("获取当前服务兵身份证照片URL")
    public R getIdCardUrl() {
        try {
            // 获取当前登录用户的身份证照片URL
            String idCardUrl = faceCompareService.getAttendantIdCardImageUrl(
                ((com.youngking.renrenwithactiviti.modules.sys.entity.SysUserEntity) 
                org.apache.shiro.SecurityUtils.getSubject().getPrincipal()).getUserId()
            );
            return R.ok().put("idCardUrl", idCardUrl);
        } catch (Exception e) {
            log.error("获取身份证照片URL失败", e);
            return R.error("获取身份证照片URL失败：" + e.getMessage());
        }
    }

    /**
     * 根据服务兵ID获取身份证照片URL（用于测试）
     * @param attendantId 服务兵ID
     * @return 身份证照片URL
     */
    @GetMapping("/idcard-url/{attendantId}")
    @ApiOperation("根据服务兵ID获取身份证照片URL")
    public R getIdCardUrlById(@PathVariable Integer attendantId) {
        try {
            String idCardUrl = faceCompareService.getAttendantIdCardImageUrlById(attendantId);
            return R.ok().put("idCardUrl", idCardUrl);
        } catch (Exception e) {
            log.error("获取身份证照片URL失败", e);
            return R.error("获取身份证照片URL失败：" + e.getMessage());
        }
    }
}
