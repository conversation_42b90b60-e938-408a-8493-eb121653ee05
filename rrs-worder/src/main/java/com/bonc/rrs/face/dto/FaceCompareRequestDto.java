package com.bonc.rrs.face.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 人脸比对请求DTO
 * <AUTHOR>
 */
@Data
@ApiModel(value = "人脸比对请求参数")
public class FaceCompareRequestDto {

    /**
     * 人脸照片URL
     */
    @ApiModelProperty(value = "人脸照片URL", required = true, example = "https://example.com/face.jpg")
    @NotBlank(message = "人脸照片URL不能为空")
    private String faceImageUrl;

    /**
     * 服务兵ID（可选，如果不传则使用当前登录用户）
     */
    @ApiModelProperty(value = "服务兵ID", required = false, example = "123")
    private Integer attendantId;
}
