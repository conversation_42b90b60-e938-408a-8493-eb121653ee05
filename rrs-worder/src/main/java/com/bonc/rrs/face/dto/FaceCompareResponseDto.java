package com.bonc.rrs.face.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 人脸比对响应DTO
 * <AUTHOR>
 */
@Data
@ApiModel(value = "人脸比对响应结果")
public class FaceCompareResponseDto {

    /**
     * 比对结果：true-同一人，false-不是同一人
     */
    @ApiModelProperty(value = "比对结果", example = "true")
    private Boolean isSamePerson;

    /**
     * 相似度分数（0-100）
     */
    @ApiModelProperty(value = "相似度分数", example = "85.5")
    private Float similarity;

    /**
     * 置信度分数（0-100）
     */
    @ApiModelProperty(value = "置信度分数", example = "90.2")
    private Float confidence;

    /**
     * 比对状态码
     */
    @ApiModelProperty(value = "比对状态码", example = "200")
    private String statusCode;

    /**
     * 比对状态信息
     */
    @ApiModelProperty(value = "比对状态信息", example = "success")
    private String statusMessage;

    /**
     * 服务兵姓名
     */
    @ApiModelProperty(value = "服务兵姓名", example = "张三")
    private String attendantName;

    /**
     * 服务兵ID
     */
    @ApiModelProperty(value = "服务兵ID", example = "123")
    private Integer attendantId;

    /**
     * 比对时间戳
     */
    @ApiModelProperty(value = "比对时间戳", example = "1640995200000")
    private Long timestamp;

    /**
     * 创建成功响应
     */
    public static FaceCompareResponseDto success(Boolean isSamePerson, Float similarity, Float confidence, 
                                                String attendantName, Integer attendantId) {
        FaceCompareResponseDto response = new FaceCompareResponseDto();
        response.setIsSamePerson(isSamePerson);
        response.setSimilarity(similarity);
        response.setConfidence(confidence);
        response.setStatusCode("200");
        response.setStatusMessage("success");
        response.setAttendantName(attendantName);
        response.setAttendantId(attendantId);
        response.setTimestamp(System.currentTimeMillis());
        return response;
    }

    /**
     * 创建失败响应
     */
    public static FaceCompareResponseDto error(String statusCode, String statusMessage) {
        FaceCompareResponseDto response = new FaceCompareResponseDto();
        response.setIsSamePerson(false);
        response.setSimilarity(0.0f);
        response.setConfidence(0.0f);
        response.setStatusCode(statusCode);
        response.setStatusMessage(statusMessage);
        response.setTimestamp(System.currentTimeMillis());
        return response;
    }
}
