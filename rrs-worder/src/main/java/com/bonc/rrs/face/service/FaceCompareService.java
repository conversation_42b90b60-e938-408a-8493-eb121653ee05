package com.bonc.rrs.face.service;

import com.bonc.rrs.face.dto.FaceCompareRequestDto;
import com.bonc.rrs.face.dto.FaceCompareResponseDto;

/**
 * 人脸比对服务接口
 * <AUTHOR>
 */
public interface FaceCompareService {

    /**
     * 人脸比对
     * @param request 比对请求参数
     * @return 比对结果
     */
    FaceCompareResponseDto compareFace(FaceCompareRequestDto request);

    /**
     * 根据用户ID获取服务兵身份证照片URL
     * @param userId 用户ID
     * @return 身份证照片URL
     */
    String getAttendantIdCardImageUrl(Long userId);

    /**
     * 根据服务兵ID获取身份证照片URL
     * @param attendantId 服务兵ID
     * @return 身份证照片URL
     */
    String getAttendantIdCardImageUrlById(Integer attendantId);
}
