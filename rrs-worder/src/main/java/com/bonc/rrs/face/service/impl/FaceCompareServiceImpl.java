package com.bonc.rrs.face.service.impl;

import com.aliyun.facebody20191230.Client;
import com.aliyun.facebody20191230.models.CompareFaceRequest;
import com.aliyun.facebody20191230.models.CompareFaceResponse;
import com.aliyun.tea.TeaException;
import com.aliyun.teautil.models.RuntimeOptions;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bonc.rrs.face.dto.FaceCompareRequestDto;
import com.bonc.rrs.face.dto.FaceCompareResponseDto;
import com.bonc.rrs.face.service.FaceCompareService;
import com.bonc.rrs.worder.dao.BizAttendantDao;
import com.bonc.rrs.worder.entity.BizAttendantEntity;
import com.bonc.rrs.workManager.dao.SysFilesMapper;
import com.bonc.rrs.workManager.entity.SysFileEntity;
import com.youngking.lenmoncore.common.exception.RRException;
import com.youngking.lenmoncore.common.utils.StringUtils;
import com.youngking.renrenwithactiviti.modules.sys.entity.SysUserEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 人脸比对服务实现类
 * <AUTHOR>
 */
@Service
@Slf4j
public class FaceCompareServiceImpl implements FaceCompareService {

    @Autowired
    private Client faceClient;

    @Autowired
    private BizAttendantDao bizAttendantDao;

    @Autowired
    private SysFilesMapper sysFilesMapper;

    @Value("${baseUrl:}")
    private String baseUrl;

    @Override
    public FaceCompareResponseDto compareFace(FaceCompareRequestDto request) {
        try {
            // 获取当前登录用户
            SysUserEntity currentUser = (SysUserEntity) SecurityUtils.getSubject().getPrincipal();
            if (currentUser == null) {
                return FaceCompareResponseDto.error("401", "用户未登录");
            }

            // 获取服务兵身份证照片URL
            String idCardImageUrl;
            String attendantName;
            Integer attendantId;

            if (request.getAttendantId() != null) {
                // 使用指定的服务兵ID
                BizAttendantEntity attendant = bizAttendantDao.selectById(request.getAttendantId());
                if (attendant == null) {
                    return FaceCompareResponseDto.error("404", "服务兵不存在");
                }
                idCardImageUrl = getAttendantIdCardImageUrlById(request.getAttendantId());
                attendantName = attendant.getName();
                attendantId = attendant.getId();
            } else {
                // 使用当前登录用户对应的服务兵
                idCardImageUrl = getAttendantIdCardImageUrl(currentUser.getUserId());
                // 根据用户ID查询服务兵信息
                QueryWrapper<BizAttendantEntity> wrapper = new QueryWrapper<>();
                wrapper.eq("user_id", currentUser.getUserId());
                BizAttendantEntity attendant = bizAttendantDao.selectOne(wrapper);
                if (attendant == null) {
                    return FaceCompareResponseDto.error("404", "当前用户不是服务兵");
                }
                attendantName = attendant.getName();
                attendantId = attendant.getId();
            }

            if (StringUtils.isBlank(idCardImageUrl)) {
                return FaceCompareResponseDto.error("404", "服务兵身份证照片不存在");
            }

            // 调用阿里云人脸比对API
            CompareFaceRequest compareFaceRequest = new CompareFaceRequest()
                    .setImageURLA(request.getFaceImageUrl())
                    .setImageURLB(idCardImageUrl);

            RuntimeOptions runtime = new RuntimeOptions();
            CompareFaceResponse response = faceClient.compareFaceWithOptions(compareFaceRequest, runtime);

            // 处理比对结果
            if (response.getBody() != null && response.getBody().getData() != null) {
                // 根据阿里云官方文档，响应字段应该是 similarity 和 confidence
                Float similarity = response.getBody().getData().similarity;
                Float confidence = response.getBody().getData().confidence;

                // 设置相似度阈值，大于80%认为是同一人
                Boolean isSamePerson = similarity != null && similarity > 80.0f;

                log.info("人脸比对完成，服务兵：{}，相似度：{}，置信度：{}", attendantName, similarity, confidence);

                return FaceCompareResponseDto.success(isSamePerson, similarity, confidence, attendantName, attendantId);
            } else {
                return FaceCompareResponseDto.error("500", "人脸比对失败，响应数据为空");
            }

        } catch (TeaException e) {
            log.error("阿里云人脸比对API调用失败", e);
            return FaceCompareResponseDto.error(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("人脸比对服务异常", e);
            return FaceCompareResponseDto.error("500", "人脸比对服务异常：" + e.getMessage());
        }
    }

    @Override
    public String getAttendantIdCardImageUrl(Long userId) {
        // 根据用户ID查询服务兵信息
        QueryWrapper<BizAttendantEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("user_id", userId);
        BizAttendantEntity attendant = bizAttendantDao.selectOne(wrapper);
        
        if (attendant == null) {
            throw new RRException("当前用户不是服务兵");
        }

        return getAttendantIdCardImageUrlById(attendant.getId());
    }

    @Override
    public String getAttendantIdCardImageUrlById(Integer attendantId) {
        BizAttendantEntity attendant = bizAttendantDao.selectById(attendantId);
        if (attendant == null) {
            throw new RRException("服务兵不存在");
        }

        String idCardImg = attendant.getIdCardImg();
        if (StringUtils.isBlank(idCardImg)) {
            throw new RRException("服务兵身份证照片不存在");
        }

        // 解析文件ID并获取文件URL
        String[] fileIds = idCardImg.split(",");
        if (fileIds.length > 0) {
            String fileId = fileIds[0]; // 取第一张身份证照片
            List<SysFileEntity> sysFiles = sysFilesMapper.listSysFiles(fileId);
            if (sysFiles != null && !sysFiles.isEmpty()) {
                SysFileEntity sysFile = sysFiles.get(0);
                String filePath = sysFile.getPath();
                if (StringUtils.isNotBlank(filePath)) {
                    // 如果路径不是完整URL，则拼接baseUrl
                    if (!filePath.startsWith("http")) {
                        return baseUrl + filePath;
                    }
                    return filePath;
                }
            }
        }

        throw new RRException("无法获取服务兵身份证照片URL");
    }
}
