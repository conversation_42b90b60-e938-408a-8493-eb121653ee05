package com.bonc.rrs.worder.service.impl;

import com.alibaba.excel.EasyExcel;
import com.baomidou.lock.annotation.Lock4j;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bonc.rrs.balanceprocess.entity.BalanceFileEntity;
import com.bonc.rrs.balanceprocess.entity.WorderChildInformationEntity;
import com.bonc.rrs.balanceprocess.service.BalanceFileService;
import com.bonc.rrs.balanceprocess.service.CompanyInvoiceService;
import com.bonc.rrs.balanceprocess.service.WorderChildInformationService;
import com.bonc.rrs.balanceprocess.vo.NoInstallReasonVo;
import com.bonc.rrs.balancerule.dao.BalanceRuleDao;
import com.bonc.rrs.balancerule.dao.BalanceRuleDetailDao;
import com.bonc.rrs.balancerule.entity.BalanceRuleDetailEntity;
import com.bonc.rrs.balancerule.entity.BalanceRuleEntity;
import com.bonc.rrs.balancerule.service.BalanceRuleService;
import com.bonc.rrs.branchbalance.dao.BranchBalanceDao;
import com.bonc.rrs.branchbalance.entity.WorderBalanceFeeDetailEntity;
import com.bonc.rrs.branchbalance.entity.WorderBalanceFeeEntity;
import com.bonc.rrs.branchbalance.entity.WorderUsedMaterielEntity;
import com.bonc.rrs.branchbalance.service.BranchBalanceService;
import com.bonc.rrs.branchbalance.service.WorderBalanceFeeDetailService;
import com.bonc.rrs.branchbalance.service.WorderUsedMaterielService;
import com.bonc.rrs.branchbalance.vo.WorderUserBalanceFeeVO;
import com.bonc.rrs.byd.domain.PushSubmitInfo;
import com.bonc.rrs.byd.enums.ConstantPool;
import com.bonc.rrs.bydbaobiao.domain.BydBaoBiaoResult;
import com.bonc.rrs.bydbaobiao.domain.BydBaobiaoParam;
import com.bonc.rrs.ca.domain.CaApiResponse;
import com.bonc.rrs.ca.service.ICaApiService;
import com.bonc.rrs.changcheng.client.GreatWallClient;
import com.bonc.rrs.changcheng.domain.SubmitInstallOrderRequest;
import com.bonc.rrs.changcheng.domain.SubmitReminderRequest;
import com.bonc.rrs.gace.enums.WorkOrderStatusEnum;
import com.bonc.rrs.gace.service.GacePushService;
import com.bonc.rrs.insuranceamount.InsuranceAmountService;
import com.bonc.rrs.insurancequota.InsuranceQuotaService;
import com.bonc.rrs.pay.dao.WorderOrderLogMapper;
import com.bonc.rrs.pay.model.entity.WorderOrderLogDTO;
import com.bonc.rrs.pay.service.WorderPayService;
import com.bonc.rrs.sapreceivable.InType;
import com.bonc.rrs.sapreceivable.OutType;
import com.bonc.rrs.sapreceivable.SapReceiveService;
import com.bonc.rrs.serviceprovider.po.BusinessProcessPo;
import com.bonc.rrs.serviceprovider.po.Result;
import com.bonc.rrs.serviceprovider.service.ProviderBusinessService;
import com.bonc.rrs.serviceprovider.service.impl.business.BusinessProcessPushCancelReviewInfo;
import com.bonc.rrs.serviceprovider.service.impl.business.BusinessProcessPushCheckCancelReviewInfo;
import com.bonc.rrs.sparesettlement.dao.BillingRecodeMapper;
import com.bonc.rrs.sparesettlement.model.entity.BillingRecodeDTO;
import com.bonc.rrs.sparesettlement.service.impl.InvoiceAPIServiceImpl;
import com.bonc.rrs.suite.dao.SuiteDetailDao;
import com.bonc.rrs.supervise.entity.SuperviseInfomation;
import com.bonc.rrs.supervise.entity.SuperviseOperationRecord;
import com.bonc.rrs.util.BalanceRuleUtils;
import com.bonc.rrs.util.Results;
import com.bonc.rrs.worder.common.FlowCommon;
import com.bonc.rrs.worder.constant.BaseConstant;
import com.bonc.rrs.worder.constant.FlowConstant;
import com.bonc.rrs.worder.constant.SceneConstant;
import com.bonc.rrs.worder.controller.WorderInformationController;
import com.bonc.rrs.worder.dao.*;
import com.bonc.rrs.worder.dto.AuditNoticeDTO;
import com.bonc.rrs.worder.dto.NoticeDisposeDTO;
import com.bonc.rrs.worder.dto.dto.*;
import com.bonc.rrs.worder.dto.vo.*;
import com.bonc.rrs.worder.entity.*;
import com.bonc.rrs.worder.entity.dto.*;
import com.bonc.rrs.worder.entity.po.*;
import com.bonc.rrs.worder.entity.vo.CSCockpitVo;
import com.bonc.rrs.worder.entity.vo.ColorLabelCount;
import com.bonc.rrs.worder.entity.vo.UserTransferVo;
import com.bonc.rrs.worder.listener.IncentiveCarListenter;
import com.bonc.rrs.worder.listener.IncentiveListenter;
import com.bonc.rrs.worder.service.*;
import com.bonc.rrs.worder.utils.UserRoleUtils;
import com.bonc.rrs.worderAudit.entity.WorderAuditResultEntity;
import com.bonc.rrs.worderAudit.service.WorderAuditResultService;
import com.bonc.rrs.worderapp.constant.Constant;
import com.bonc.rrs.worderapp.dao.WorderOperationRecodeDao;
import com.bonc.rrs.worderapp.dao.WorderOrderDao;
import com.bonc.rrs.worderapp.entity.WorderOperationRecodeEntity;
import com.bonc.rrs.worderapp.entity.po.FieldPo;
import com.bonc.rrs.worderapp.service.WorderOperationRecodeService;
import com.bonc.rrs.worderapp.service.impl.WorderOrderServiceImpl;
import com.bonc.rrs.worderinformationaccount.constant.BalanceProperties;
import com.bonc.rrs.worderinformationaccount.dao.WorderPmStimulateDao;
import com.bonc.rrs.worderinformationaccount.entity.WorderPmStimulateEntity;
import com.bonc.rrs.worderinformationaccount.service.WorderInformationAccountService;
import com.bonc.rrs.worderinvoice.entity.WorderWaitAccountEntity;
import com.bonc.rrs.worderinvoice.service.WorderWaitAccountService;
import com.bonc.rrs.workManager.dao.AutoSendMapper;
import com.bonc.rrs.workManager.dao.BizRegionMapper;
import com.bonc.rrs.workManager.dao.SysFilesMapper;
import com.bonc.rrs.workManager.dao.WorkMsgDao;
import com.bonc.rrs.workManager.entity.BizRegion;
import com.bonc.rrs.workManager.entity.OperationRecord;
import com.bonc.rrs.workManager.service.AutoSendService;
import com.common.pay.common.utils.DateUtil;
import com.gexin.fastjson.JSON;
import com.google.common.collect.Lists;
import com.youngking.lenmoncore.common.constant.IntegerEnum;
import com.youngking.lenmoncore.common.constant.WarningConstant;
import com.youngking.lenmoncore.common.exception.RRException;
import com.youngking.lenmoncore.common.utils.*;
import com.youngking.renrenwithactiviti.modules.sys.dao.BrandDao;
import com.youngking.renrenwithactiviti.modules.sys.dao.ExcuteSqlMapper;
import com.youngking.renrenwithactiviti.modules.sys.entity.*;
import com.youngking.renrenwithactiviti.modules.sys.service.SysDictionaryDetailService;
import com.youngking.renrenwithactiviti.modules.sys.service.SysRoleService;
import com.youngking.renrenwithactiviti.modules.sys.service.SysUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * 工单信息业务层
 */
@Slf4j
@Service("worderInformationService")
public class WorderInformationServiceImpl extends ServiceImpl<WorderInformationDao, WorderInformationEntity> implements WorderInformationService {

    @Resource
    public WorderVersionDao worderVersionDao;
    @Resource
    public WorderOperationRecodeDao worderOperationRecodeDao;
    @Autowired
    private SysRoleService sysRoleService;
    @Autowired
    private WorderExtFieldService worderExtFieldService;
    @Autowired
    private WorderRemarkLogService worderRemarkLogService;
    @Autowired
    private CompanyInformationService companyInformationService;
    @Autowired(required = false)
    private WorderTemplateDao worderTemplateDao;
    @Autowired
    private WorderUsedMaterielService worderUsedMaterielService;
    @Autowired
    private WorderTypeService worderTypeService;
    @Autowired(required = false)
    private WorkMsgDao workMsgDao;
    @Autowired(required = false)
    private BalanceRuleDao balanceRuleDao;
    @Autowired(required = false)
    private BalanceRuleDetailDao balanceRuleDetailDao;
    @Autowired(required = false)
    private SuiteDetailDao suiteDetailDao;
    @Autowired(required = false)
    private BalanceProperties balanceProperties;
    @Resource
    public BranchBalanceDao branchBalanceDao;
    @Autowired(required = false)
    ExcuteSqlMapper excuteSqlMapper;
    @Autowired(required = false)
    private BizRegionMapper bizRegionMapper;
    @Autowired
    private SysUserService sysUserService;
    @Autowired
    private WorderOrderServiceImpl worderOrderService;
    @Autowired(required = false)
    private WorderOrderDao worderOrderDao;
    @Autowired(required = false)
    private ExtFieldDao extFieldDao;
    @Autowired
    WorderTemplateRegionService worderTemplateRegionService;
    @Autowired(required = false)
    SysFilesMapper sysFilesMapper;
    @Autowired
    private BranchBalanceService branchBalanceService;
    @Autowired
    private InsuranceAmountService insuranceAmountService;
    @Autowired
    private InsuranceQuotaService insuranceQuotaService;
    @Autowired
    private SapReceiveService sapReceiveService;
    @Autowired(required = false)
    private WorderInformationDao worderInformationDao;
    @Autowired(required = false)
    private CompanyInvoiceService companyInvoiceService;
    @Autowired(required = false)
    BillingRecodeMapper billingRecodeMapper;
    @Autowired(required = false)
    WorderPayService worderPayService;
    @Autowired(required = false)
    WorderWaitAccountService worderWaitAccountService;
    @Autowired(required = false)
    WorderOrderLogMapper worderOrderLogMapper;
    @Autowired(required = false)
    InvoiceAPIServiceImpl invoiceAPIService;
    @Autowired(required = false)
    CompanyBrandCarService companyBrandCarService;
    @Autowired
    WorderBalanceFeeDetailService worderBalanceFeeDetailService;
    @Autowired(required = false)
    private BrandDao brandDao;
    @Resource
    private BizRegionDao bizRegionDao;
    @Autowired
    private DotInformationDao dotInformationDao;
    @Autowired
    private WorderPmStimulateDao worderPmStimulateDao;
    @Autowired
    private WorderSceneService worderSceneService;
    @Autowired
    private WorderInformationAccountService worderInformationAccountService;
    @Autowired
    private BalanceFileService balanceFileService;
    @Autowired(required = false)
    private WorderInformationSubService worderInformationSubService;
    @Autowired
    private WorderChildInformationService worderChildInformationService;

    @Autowired
    private WorderInformationController worderInformationController;

    @Autowired
    private FlowCommon flowCommon;

    @Autowired
    private BalanceRuleService balanceRuleService;

    @Autowired
    private SysDictionaryDetailService sysDictionaryDetailService;

    @Autowired(required = false)
    private AutoSendService autoSendService;

    @Autowired(required = false)
    private AutoSendMapper autoSendMapper;

    @Autowired
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Autowired
    private WorderExtFieldDao worderExtFieldDao;
    @Autowired
    private BizAttendantDao bizAttendantDao;
    @Autowired
    private WorderInformationAttributeDao worderInformationAttributeDao;
    @Autowired
    private ProviderBusinessService providerBusinessService;
    @Autowired
    private WorderAuditResultService worderAuditResultService;
    @Autowired(required = false)
    private ICaApiService caApiService;
    protected static ExecutorService executorService = Executors.newCachedThreadPool();

    @Autowired
    private BusinessProcessPushCancelReviewInfo businessProcessPushCancelReviewInfo;

    @Autowired
    private BusinessProcessPushCheckCancelReviewInfo businessProcessPushCheckCancelReviewInfo;

    @Autowired
    private WorderBuffService worderBuffService;

    @Autowired(required = false)
    FlowWorderDao flowWorderDao;
    @Autowired
    private WorderOperationRecodeService worderOperationRecodeService;
    @Autowired
    private GacePushService gacePushService;

    private static String BRANCH_ISTRATOR = "网点管理员";

    @Autowired
    private RedisUtils redisUtils;

    @Autowired
    private GreatWallClient changChengWallClient;

    @Autowired
    private WorderRemarkLogDao worderRemarkLogDao;

//    void test(String s1,Integer k) {
//        for (int i = 0; i < k; i++) {
//            Thread t1 = new Thread(() -> worderNoMaker(s1));
//            t1.start();
//        }
//
//        Thread t2 = new Thread(() -> worderNoMaker(s1));
//        Thread t3 = new Thread(() -> worderNoMaker(s1));
//        Thread t4 = new Thread(() -> worderNoMaker(s1));
//        Thread t5 = new Thread(() -> worderNoMaker(s1));
//
//        t2.start();
//        t3.start();
//        t4.start();
//        t5.start();
//    }

    //乐观锁生成工单序号 (省市简称+创建日期+0-1000的自增序列)
    @Override
    public String worderNoMaker(String abbreviation) {
//		WorderVersionEntity worderVersionEntity = worderVersionDao.selectOne(new QueryWrapper<>(WorderVersionEntity.builder().time(LocalDate.now()).build()));
//		if (worderVersionEntity != null) {
//			worderVersionEntity.setSort(worderVersionEntity.getSort() + 1);
//			worderVersionDao.updateById(worderVersionEntity);
//		} else {
//			worderVersionEntity = WorderVersionEntity.builder()
//					.time(LocalDate.now())
//					.sort(1)
//					.version(0)
//					.build();
//
//			worderVersionDao.insert(worderVersionEntity);
//		}
        String currDate = DateUtil.getCurrDate(DateUtil.LONG_DATE_FORMAT);
        return abbreviation + currDate + replenish(BaseConstant.NOPRE.WORDERNO + currDate);
    }

    public Long getWorderVersion(String key) {
        return redisUtils.incrementKey(key);
    }


    public String replenish(String key) {
        Long worderVersion = getWorderVersion(key);
        return String.format("%06d", worderVersion);
    }

    /**
     * 获取品牌信息
     *
     * @return
     */
    @Override
    public List<BrandPo> getBrandList(Integer templateId) {
        Long userId = getUser().getUserId();
        //获取用户角色信息
        List<SysRoleEntity> userList = sysRoleService.getByUserId(userId);
        List<BrandPo> brandPos = new ArrayList<>();
        if (UserRoleUtils.isBranch(userList)) {//网点
            brandPos = worderTemplateDao.listByDotId(getUser().getUserId());
        } else {
            brandPos = worderTemplateDao.listBrand(templateId, getUser().getUserId());
        }

        return brandPos;
    }

    /**
     * 获取品牌信息
     *
     * @return
     */
    @Override
    public List<BrandEntity> getBrandListInfo(Integer templateId) {
        List<BrandEntity> brandPos = worderTemplateDao.listBrandInfo(templateId, getUser().getUserId());
        return brandPos;
    }

    @Override
    public List<BrandPo> listBrands() {
        return worderTemplateDao.listBrands();
    }

    @Override
    public List<BrandEntity> listBrandInfos() {
        return worderTemplateDao.listBrandInfos();
    }

    /**
     * 获取支持导入的品牌列表
     *
     * @return
     */
    @Override
    public List<BrandEntity> listImportBrandInfos() {
        return worderTemplateDao.listImportBrandInfos();
    }

    /**
     * 根据品牌查询车型
     *
     * @param brandId
     * @return
     */
    @Override
    public List<CarTypePo> getCarTypeList(String brandId) {
        List<CarTypePo> carTypePos = worderTemplateDao.listCarTypeByBrandId(brandId);
        return carTypePos;
    }

    /**
     * 根据品牌车型筛选匹配的工单模板
     *
     * @return
     */
    @Override
    public List<WorderTemplatePo> getWorderTemplate(Map<String, Object> params) {
        params.put("userId", getUser().getUserId());
        List<WorderTemplatePo> worderTemplatePos = baseMapper.getWorderTemplate(params);
        return worderTemplatePos;
    }

    /**
     * 工单信息 根据品牌车型筛选匹配的工单模板
     *
     * @return
     */
    @Override
    public List<WorderTemplatePo> worderTemplate(Map<String, Object> params) {
        List<WorderTemplatePo> worderTemplatePos = baseMapper.getWorderTemplate(params);
        return worderTemplatePos;
    }

    /**
     * 根据品牌车型筛选匹配的工单模板
     *
     * @return
     */
    @Override
    public List<WorderTemplateDto> getWorderTemplateInfos(Map<String, Object> params) {
        params.put("userId", getUser().getUserId());
        List<WorderTemplateDto> worderTemplateDto = baseMapper.getWorderTemplateInfos(params);
        return worderTemplateDto;
    }

    /**
     * 根据模板id查询工单字段
     *
     * @param templateId
     * @return
     */
    @Override
    public WorderInformationVo getWorderFieldList(Integer templateId) {
        List<FieldPo> fieldPos = worderOrderDao.listWorderFieldByTemplateId(templateId);
        String brandId = baseMapper.getBrand(templateId).getBrandId();
        List<String> companyId = baseMapper.getCompanyId(brandId);
        String companSql;
        if (companyId.isEmpty()) {
            companSql = "(999999999999999)";
        } else {
            companSql = companyId.stream().collect(Collectors.joining(", ", "(", ")"));
        }
        List<FieldPo> fieldData = worderOrderService.getFieldData(fieldPos, companSql);
        List<FieldPo> extField = fieldData.stream().map(fieldPo -> {
            if ("9".equals(fieldPo.getFieldType())) {
                fieldPo.setInstallRegionList(worderTemplateRegionService.listWordTemplateRegion(String.valueOf(templateId)));
            }
            if ("10".equals(fieldPo.getFieldType())) {
                fieldPo.setWorderType(baseMapper.getWorderType(templateId));
            }
            return fieldPo;
        }).collect(Collectors.toList());
        WorderInformationVo worderInformationVo = new WorderInformationVo();
        //worderInformationVo.setExtFieldList(extField);
        worderInformationVo.setBrand(baseMapper.getBrand(templateId));
        worderInformationVo.setCarTypeName(baseMapper.getCarType(templateId));
        worderInformationVo.setWorderType(baseMapper.getWorderType(templateId));
        worderInformationVo.setBizRegionList(worderTemplateRegionService.listRegion(String.valueOf(templateId)));
        List<FieldPo> extFieldList = new ArrayList<>();
        SettleObjectVo settleObjectVo = baseMapper.getSettleObject(templateId);
        if (settleObjectVo.getSettleId() == 1) {
            settleObjectVo.setSettleName("车企");
            extFieldList = fieldData.stream().map(o -> {
                if ("companyId".equals(o.getFieldDesc())) {
                    o.setNotNull("1");
                    o.setCompanyType(1);
                }
                return o;
            }).collect(Collectors.toList());
        }
        if (settleObjectVo.getSettleId() == 2) {
            settleObjectVo.setSettleName("分中心");
            extFieldList = fieldData.stream().map(o -> {
                if ("fzx".equals(o.getFieldDesc())) {
                    o.setNotNull("1");
                    o.setCompanyType(2);
                }
                return o;
            }).collect(Collectors.toList());
        }
        if (settleObjectVo.getSettleId() == 3) {
            settleObjectVo.setSettleName("经销商");
            extFieldList = fieldData.stream().map(o -> {
                if ("jxs".equals(o.getFieldDesc())) {
                    o.setNotNull("1");
                    o.setCompanyType(3);
                }
                return o;
            }).collect(Collectors.toList());
        }
        worderInformationVo.setExtFieldList(extFieldList);
        worderInformationVo.setSettleObjectVo(settleObjectVo);
        return worderInformationVo;
    }

    @Override
    public WorderInformationVo getWorderExtInformation(String worderNo) {
        List<FieldPo> fieldPos = baseMapper.listWorderFieldByWorderNo(worderNo);
        WorderInformationVo worderInformationVo = new WorderInformationVo();
        if (fieldPos.size() > 0 && fieldPos.get(0) != null) {
            Integer templateId = fieldPos.get(0).getTemplateId();
            String brandId = baseMapper.getBrand(templateId).getBrandId();
            List<String> companyId = baseMapper.getCompanyId(brandId);
            String companSql = "(";
            if (companyId.isEmpty()) {
                companSql = "(999999999999999)";
            } else {
                for (int i = 0; i < companyId.size() - 1; i++) {
                    companSql = companSql + companyId.get(i) + ",";
                }
                companSql = companSql + companyId.get(companyId.size() - 1) + ")";
            }

            List<FieldPo> fieldData = worderOrderService.getFieldData(fieldPos, companSql);
//            List<FieldPo> extField = fieldData.stream().map(fieldPo -> {
//                // 安装地址
//                if ("9".equals(fieldPo.getFieldType())) {
//                    fieldPo.setInstallRegionList(worderTemplateRegionService.listWordTemplateRegion(String.valueOf(templateId)));
//                }
//                // 工单类型
//                if ("10".equals(fieldPo.getFieldType())) {
//                    fieldPo.setWorderType(baseMapper.getWorderType(templateId));
//                }
//                // 资料
//                if (Constant.STRING_ONE.equals(fieldPo.getFieldClass())) {
//                    // 前后加上大括号
//                    String fileIds = com.youngking.lenmoncore.common.utils.StringUtils.appendSqureBeforeAndAfter(fieldPo.getFieldValues());
//                    // 返回所有URL
//                    List<String> filePaths = sysFilesMapper.listPaths(fileIds);
//                    fieldPo.setFieldValue(filePaths);
//                    if (filePaths.size() > 0) {
//                        fieldPo.setFieldValues(filePaths.get(0));
//                    }
//                }
//                return fieldPo;
//            }).collect(Collectors.toList());
            List<FieldPo> extField = fieldData.stream().map(fieldPo -> {
                if ("9".equals(fieldPo.getFieldType())) {
                    fieldPo.setInstallRegionList(worderTemplateRegionService.listWordTemplateRegion(String.valueOf(templateId)));
                }
                if ("10".equals(fieldPo.getFieldType())) {
                    fieldPo.setWorderType(baseMapper.getWorderType(templateId));
                }
                return fieldPo;
            }).collect(Collectors.toList());
            List<FieldPo> extFieldList = new ArrayList<>();
            SettleObjectVo settleObjectVo = baseMapper.getSettleObject(templateId);
            if (settleObjectVo.getSettleId() == 1) {
                settleObjectVo.setSettleName("车企");
                extFieldList = fieldData.stream().map(o -> {
                    if ("companyId".equals(o.getFieldDesc())) {
                        o.setNotNull("1");
                        o.setCompanyType(1);
                    }
                    return o;
                }).collect(Collectors.toList());
            }
            if (settleObjectVo.getSettleId() == 2) {
                settleObjectVo.setSettleName("分中心");
                extFieldList = fieldData.stream().map(o -> {
                    if ("fzx".equals(o.getFieldDesc())) {
                        o.setNotNull("1");
                        o.setCompanyType(2);
                    }
                    return o;
                }).collect(Collectors.toList());
            }
            if (settleObjectVo.getSettleId() == 3) {
                settleObjectVo.setSettleName("经销商");
                extFieldList = fieldData.stream().map(o -> {
                    if ("jxs".equals(o.getFieldDesc())) {
                        o.setNotNull("1");
                        o.setCompanyType(3);
                    }
                    return o;
                }).collect(Collectors.toList());
            }
//            worderInformationVo.setExtFieldList(extField);
            worderInformationVo.setExtFieldList(extFieldList);
            worderInformationVo.setBrand(baseMapper.getBrand(templateId));
            worderInformationVo.setCarTypeName(baseMapper.getCarType(templateId));
            worderInformationVo.setWorderType(baseMapper.getWorderType(templateId));
            worderInformationVo.setBizRegionList(worderTemplateRegionService.listRegion(String.valueOf(templateId)));
//            SettleObjectVo settleObjectVo = baseMapper.getSettleObject(templateId);
//            if (settleObjectVo.getSettleId() == 1) {
//                settleObjectVo.setSettleName("车企");
//            }
//            if (settleObjectVo.getSettleId() == 2) {
//                settleObjectVo.setSettleName("分中心");
//            }
//            if (settleObjectVo.getSettleId() == 3) {
//                settleObjectVo.setSettleName("经销商");
//            }
            worderInformationVo.setSettleObjectVo(settleObjectVo);
        }

        return worderInformationVo;
    }

    /**
     * 校验厂商是否能够创建工单
     *
     * @param resultVo
     */
    @Override
    public CheckResultVo checkCompany(CheckResultVo resultVo) {
        System.out.println(resultVo.getCompanyId());
        System.out.println(resultVo.getSettleId());
        resultVo.setState(1);
        resultVo.setResultMessage("厂商校验通过");
        //保险金额校验
        CompanyInformationEntity companyInformation = baseMapper.getByCompanyId(resultVo.getCompanyId());
        //根据车企id获取品牌id
        CompanyBrandCarEntity info = companyBrandCarService.selectInfoByCompanyId(resultVo.getCompanyId());
        Integer brandId = info.getBrandId();
        //System.out.println(companyInformation);
        //获取360保险额度
        CustomerGuaranteeQuotaEntity quota = baseMapper.getQuota(companyInformation.getCompanyCode(), "0RK0");
        if (quota != null) {
            BigDecimal newIdue = quota.getIdue61().subtract(quota.getBeforeMarchIdue());
            if (newIdue.compareTo(new BigDecimal(0)) == 1) {
                resultVo.setState(0);
                resultVo.setResultMessage("回款逾期超过60天，不能新建工单！");
                return resultVo;
            }
            if ((brandId != null && brandId != 18) && (quota.getQuotaSum() == null || (quota.getQuotaSum().compareTo(new BigDecimal(0)) == 0))) {
                resultVo.setState(0);
                resultVo.setResultMessage("360额度超标，不能新建工单！");
                return resultVo;
            } else {

                InType in = new InType();
                in.setBUKRS("0RK0");
                in.setKUNNR(companyInformation.getCompanyCode());
                in.setFLAG("");
                in.setSYSNAME("GVS");
                OutType outTypes = sapReceiveService.outTypeList(in);  //查询sap实时应收余额
                Map<String, Object> map = new HashMap<>();
                BigDecimal companyFeeSum = new BigDecimal(0);
                if (resultVo.getSettleId() == 1) {
                    //结算对象是车企
                    map.put("companyType", "companyId");
                    map.put("companyId", resultVo.getCompanyId());
                    companyFeeSum = baseMapper.getCompanyFeeSum(map);
                } else if (resultVo.getSettleId() == 2) {
                    //结算对象是分中心
                    map.put("companyType", "fzx");
                    map.put("companyId", resultVo.getCompanyId());
                    companyFeeSum = baseMapper.getCompanyFeeSum(map);
                } else if (resultVo.getSettleId() == 3) {
                    //结算对象是经销商
                    map.put("companyType", "jxs");
                    map.put("companyId", resultVo.getCompanyId());
                    companyFeeSum = baseMapper.getCompanyFeeSum(map);
                }
                if (companyFeeSum == null) {
                    companyFeeSum = new BigDecimal(0);
                }
                BigDecimal fee = quota.getQuotaSum().subtract(outTypes.getHSLQM()).subtract(companyFeeSum);
                if (brandId != null && brandId != 18) {
                    if (fee.compareTo(new BigDecimal(0)) == 0 || fee.compareTo(new BigDecimal(0)) == -1) {
                        resultVo.setState(0);
                        resultVo.setResultMessage("360额度超标，不能新建工单！");
                        return resultVo;
                    }
                }
            }
        }


        return resultVo;
    }

    @Override
    public boolean validateCompanyOrderNumberExsit(String companyOrderNumber) {
        List<WorderInformationEntity> worderInformations = baseMapper.selectList(new QueryWrapper<WorderInformationEntity>()
                .eq("is_delete", 0).eq("company_order_number", companyOrderNumber)
                .ne("worder_status", 6).ne("worder_exec_status", 21));
        if (worderInformations.size() > IntegerEnum.ZERO.getValue()) {
            return true;
        }
        return false;
    }

    @Override
    public boolean validateCompanyOrderNumberAndBrandExsit(String companyOrderNumber, Integer templateId) {
        WorderTemplateDto template = worderTemplateDao.findTemplateInfoById(templateId);
        String brandId = null;
        if (null != template) {
            brandId = template.getBrandId();
        }
        Integer count = baseMapper.getCountByCompanyOrderNumberAndBrand(companyOrderNumber, brandId);
        if (count > IntegerEnum.ZERO.getValue()) {
            return true;
        }
        return false;
    }


    @Override
    public boolean validateCompanyOrderNumberAndBrandExsitByBrandId(String companyOrderNumber, String brandId) {
        Integer count = baseMapper.getCountByCompanyOrderNumberAndBrand(companyOrderNumber, brandId);
        if (count > IntegerEnum.ZERO.getValue()) {
            return true;
        }
        return false;
    }

    /**
     * 增加工单信息
     *
     * @param worderInformation
     */
    @Override
    @Transactional
    public String saveWorderInformation(WorderInfoEntity worderInformation, Map<Integer, WorderTemplateDto> templateDtoMap) {
        String[] regionIds = worderInformation.getAddress().split("_");
        String province = bizRegionMapper.areaName(Integer.parseInt(regionIds[0]));   //省简称
        String city = bizRegionMapper.areaName(Integer.parseInt(regionIds[1]));   //市简称
        String abbreviation = province + city;
        String no = worderNoMaker(abbreviation);
        //添加操作记录
        OperationRecord operationRecord = new OperationRecord();
        SysUserEntity user = getUser();
        operationRecord.setUserId(user.getUserId());
        operationRecord.setOperationUser(user.getUsername());
        Integer worderTypeId = worderInformation.getWorderTypeId();  //工单类型id
        String worderTypeName = worderTypeService.getById(worderTypeId).getName(); //工单类型名称
        String record = user.getUsername() + "创建" + worderTypeName + "工单";
        operationRecord.setRecord(record);
        operationRecord.setType(1);
        operationRecord.setWorderNo(no);
        operationRecord.setWorderStatus(Constant.STRING_ZERO);
        operationRecord.setWorderExecStatus(Constant.STRING_ZERO);
        workMsgDao.insertOperation(operationRecord);
        //查询工单模版
        WorderTemplateDto template = worderTemplateDao.findTemplateInfoById(worderInformation.getTemplateId());

        Boolean addMaterial = false;
        //添加工单信息
        /*if(worderInformation.getTemplateId() != null){
			BrandPo brand = baseMapper.getBrand(worderInformation.getTemplateId());
			worderInformation.setCarBrand(brand.getBrandName());  //品牌名称
		}*/
        //新建的工单轮流分配给客服
        List<SysRoleEntity> list = sysRoleService.getByUserId(user.getUserId());
        worderInformation.setAreaId(Integer.parseInt(regionIds[2]));  //添加区域id
        if (UserRoleUtils.isAdmin(list)) {
            worderInformation.setCreateBy(user.getUserId()); //客服
        } else {
            //根据工单模板查询数据字段，判断是否存在派单给指定客服
            SysDicEntity params = new SysDicEntity();
            params.setDicNumber("worderTemplateSendCreateBy");
            params.setDetailNumber(worderInformation.getTemplateId().toString());
            List<SysDictionaryDetailEntity> dictionarys = sysDictionaryDetailService.getDictionary(params);
            if (dictionarys != null && !dictionarys.isEmpty() && com.youngking.lenmoncore.common.utils.StringUtils.isNotBlank(dictionarys.get(0).getDetailName())) {
                worderInformation.setCreateBy(Long.parseLong(dictionarys.get(0).getDetailName()));
            } else {
                //正常轮流分配给客服逻辑
                Map<String, Object> map = distributeWorder(worderInformation.getTemplateId(), worderInformation.getAreaId(), template.getServiceTypeEnum());
                Long createby = (Long) map.get("userId");
                Long temp = -1L;
                if (createby.longValue() == temp) {
                    return "error:" + map.get("msg");
                }
                worderInformation.setCreateBy(createby);  //客服
            }
        }
        worderInformation.setWorderNo(no);
        worderInformation.setWorderStatus(7);
        worderInformation.setWorderStatusValue("工单预创建");
        worderInformation.setWorderExecStatus(0);
        worderInformation.setWorderExecStatusValue("未派单");
        worderInformation.setCreator(user.getUserId());   //添加创建人
        worderInformation.setCandidate(user.getUsername());  //当前办理人
        if (StringUtils.isNotBlank(worderInformation.getUserPhone()) && worderInformation.getUserPhone().length() >= 11) {
            worderInformation.setUserPhone(worderInformation.getUserPhone().substring(0, 11));//电话
        } else {
            worderInformation.setUserPhone(worderInformation.getUserPhone());//电话
        }
        WorderInfoEntity worderInformationEntity = handleRedundant(worderInformation);
        List<WorderExtFieldEntity> fieldEntities = worderInformation.getWorderExtFieldList();
        for (WorderExtFieldEntity worderExtFieldEntity : fieldEntities) {
            //理想增加物料
            if (template.getBrandId().equals("92") &&  worderExtFieldEntity.getFieldName().equals("是否预勘测订单") && worderExtFieldEntity.getFieldValue().equals("是")) {
                addMaterial = true;
            }
            if (worderExtFieldEntity.getFieldName().equals("卡泰驰SN编码")) {
                if (worderExtFieldEntity.getFieldValue().length() == 24) {
                    String str = worderExtFieldEntity.getFieldValue().substring(0, 4);
                    for (WorderExtFieldEntity extFieldEntity : fieldEntities) {
                        if (extFieldEntity.getFieldName().equals("卡泰驰厂商")) {
                            String name = baseMapper.getFactoryName(str);
                            extFieldEntity.setFieldValue(name);
                        }
                    }
                    break;
                } else {
                    return "errorLength";
                }
            }
        }
        //this.save(worderInformationEntity);
        baseMapper.addWorderInfo(worderInformationEntity);
        List<WorderExtFieldEntity> worderExtFieldList = worderInformation.getWorderExtFieldList();
        if (!CollectionUtils.isEmpty(worderExtFieldList)) {
            worderExtFieldList = worderInformation.getWorderExtFieldList().stream()
                    .map(worderExtFieldEntity -> {
                        worderExtFieldEntity.setWorderNo(no);
                        worderExtFieldEntity.setCreateTime(new Date());
                        return worderExtFieldEntity;
                    })
                    .collect(Collectors.toList());
            //添加扩展字段
            worderExtFieldService.saveBatch(worderExtFieldList);
        }
        List<String> bydBrandList = new ArrayList<>();
        //比亚迪海洋
        bydBrandList.add("21");
        bydBrandList.add("22");
        bydBrandList.add("87");
        bydBrandList.add("88");
        bydBrandList.add("89");
        bydBrandList.add("90");
        bydBrandList.add("91");
        //如果是比亚迪 添加物料
        if(bydBrandList.contains(template.getBrandId())){
            Integer brandId = Integer.parseInt(template.getBrandId());
            //添加物料
            WorderUsedMaterielEntity w1 = new WorderUsedMaterielEntity();
            w1.setWorderId(worderInformation.getWorderId());
            w1.setMaterielId(1095);
            w1.setNum(new BigDecimal(1));
            w1.setBrandId(brandId);
            worderUsedMaterielService.save(w1);

            //添加物料
            WorderUsedMaterielEntity w2 = new WorderUsedMaterielEntity();
            w2.setWorderId(worderInformation.getWorderId());
            w2.setMaterielId(1096);
            w2.setNum(new BigDecimal(1));
            w2.setBrandId(brandId);
            worderUsedMaterielService.save(w2);

            //添加物料
            WorderUsedMaterielEntity w3 = new WorderUsedMaterielEntity();
            w3.setWorderId(worderInformation.getWorderId());
            w3.setMaterielId(1097);
            w3.setNum(new BigDecimal(1));
            w3.setBrandId(brandId);
            worderUsedMaterielService.save(w3);
        }
        if(addMaterial){
            //理想增加物料
            WorderUsedMaterielEntity entity = new WorderUsedMaterielEntity();
            entity.setWorderId(worderInformation.getWorderId());
            entity.setMaterielId(65);
            entity.setNum(new BigDecimal(1));
            entity.setBrandId(92);
            worderUsedMaterielService.save(entity);
        }
        // 场景匹配
        templateDtoMap.put(worderInformationEntity.getTemplateId(), template);
        Map<String, String> sceneRuleMap = new LinkedHashMap<>();
        if (template != null && StringUtils.isNotBlank(template.getBrandId())) {
            sceneRuleMap.put(SceneConstant.Rule.brand.getName(), template.getBrandId());
        }
        sceneRuleMap.put(SceneConstant.Rule.worderTemplate.getName(), worderInformationEntity.getTemplateId() + "");
        sceneRuleMap.put(SceneConstant.Rule.worderType.getName(), worderInformationEntity.getWorderTypeId() + "");
        worderSceneService.matchScene(worderInformationEntity.getWorderId(), sceneRuleMap);
        return no;
    }

    /**
     * 增加工单信息
     *
     * @param worderInformation
     */
    @Override
    @Transactional
    public R saveWorderInformationByServiceProvider(WorderInfoEntity worderInformation) {
        String[] regionIds = worderInformation.getAddress().split("_");
        String province = bizRegionMapper.areaName(Integer.parseInt(regionIds[0]));   //省简称
        String city = bizRegionMapper.areaName(Integer.parseInt(regionIds[1]));   //市简称
        String abbreviation = province + city;
        String no = worderNoMaker(abbreviation);
        log.info("生成工单号 {}", no);

        //添加操作记录
        OperationRecord operationRecord = new OperationRecord();
        SysUserEntity user = new SysUserEntity();
        user.setUserId(worderInformation.getCreator());
        user.setUsername(worderInformation.getCandidate());
        operationRecord.setUserId(user.getUserId());
        operationRecord.setOperationUser(user.getUsername());
        Integer worderTypeId = worderInformation.getWorderTypeId();  //工单类型id
        String worderTypeName = worderTypeService.getById(worderTypeId).getName(); //工单类型名称
        String record = user.getUsername() + "创建" + worderTypeName + "工单";
        operationRecord.setRecord(record);
        operationRecord.setType(1);
        operationRecord.setWorderNo(no);
        operationRecord.setWorderStatus(Constant.STRING_ZERO);
        operationRecord.setWorderExecStatus(Constant.STRING_ZERO);
        workMsgDao.insertOperation(operationRecord);
        //查询工单模版
        WorderTemplateDto template = worderTemplateDao.findTemplateInfoById(worderInformation.getTemplateId());

        worderInformation.setWorderNo(no);
        worderInformation.setWorderStatus(7);
        worderInformation.setWorderStatusValue("工单预创建");
        worderInformation.setWorderExecStatus(0);
        worderInformation.setWorderExecStatusValue("未派单");

        worderInformation.setAreaId(Integer.parseInt(regionIds[2]));  //添加区域id
        if (StringUtils.isNotBlank(worderInformation.getUserPhone()) && worderInformation.getUserPhone().length() >= 11) {
            worderInformation.setUserPhone(worderInformation.getUserPhone().substring(0, 11));//电话
        } else {
            worderInformation.setUserPhone(worderInformation.getUserPhone());//电话
        }

        long worderId = 0L;
        try {

            //新建的工单轮流分配给客服
            List<CustormerWorderNumDto> numList = baseMapper.getCustormerWorderNum(template.getBrandId());
            if (numList != null && !numList.isEmpty()) {
                Long custormerId = numList.get(0).getCustormerId(); //客服
                baseMapper.updateWorderNum(numList.get(0));
                worderInformation.setCreateBy(custormerId);
            }

            WorderInfoEntity worderInformationEntity = ((WorderInformationServiceImpl) AopContext.currentProxy()).saveWorderInfoEntity(worderInformation);

            worderId = worderInformationEntity.getWorderId();
            List<WorderExtFieldEntity> worderExtFieldList = worderInformation.getWorderExtFieldList();
            if (!CollectionUtils.isEmpty(worderExtFieldList)) {
                worderExtFieldList = worderInformation.getWorderExtFieldList().stream()
                        .map(worderExtFieldEntity -> {
                            worderExtFieldEntity.setWorderNo(no);
                            worderExtFieldEntity.setCreateTime(new Date());
                            return worderExtFieldEntity;
                        })
                        .collect(Collectors.toList());
                //添加扩展字段
                worderExtFieldService.saveBatch(worderExtFieldList);
            }

            if (StringUtils.isNotBlank(worderInformation.getPushOrderWorderSource())) {
                WorderInformationAttributeEntity entity = new WorderInformationAttributeEntity();
                entity.setWorderId(worderInformationEntity.getWorderId());
                entity.setAttributeCode("worder_source");
                entity.setAttributeName("工单来源");
                entity.setAttributeValue(worderInformation.getPushOrderWorderSource());
                entity.setAttribute("pushOrder");
                if ("byd".equals(worderInformation.getPushOrderWorderSource())) {
                    //byd维修  是否可维护: 0-否 该订单不支持回传） 1-是
                    String isMaintainable = worderInformation.getIsMaintainable();
                    if (worderInformation.getWorderTypeId()  == 6 &&"否".equals(isMaintainable)) {
                        entity.setIsDelete(1);
                    }
                }
                worderInformationAttributeDao.insert(entity);
                if ("jl".equals(worderInformation.getPushOrderWorderSource())) {
                    WorderInformationAttributeEntity entity1 = new WorderInformationAttributeEntity();
                    entity1.setWorderId(worderInformationEntity.getWorderId());
                    entity1.setAttributeCode("company_order_id");
                    entity1.setAttributeName("车企端订单id");
                    entity1.setAttributeValue(worderInformation.getCompanyOrderNumberId());
                    entity1.setAttribute("jl");
                    worderInformationAttributeDao.insert(entity1);

                }

                if ("gace".equals(worderInformation.getPushOrderWorderSource())) {
                    WorderInformationAttributeEntity entity1 = new WorderInformationAttributeEntity();
                    entity1.setWorderId(worderInformationEntity.getWorderId());
                    entity1.setAttributeCode("company_status");
                    entity1.setAttributeName("车企状态");
                    entity1.setAttributeValue("DRAFT");
                    entity1.setAttribute("gace");
                    worderInformationAttributeDao.insert(entity1);
                }


                if ("ca".equals(worderInformation.getPushOrderWorderSource())) {
                    WorderInformationAttributeEntity entity1 = new WorderInformationAttributeEntity();
                    entity1.setWorderId(worderInformationEntity.getWorderId());
                    entity1.setAttributeCode("push_overProof");
                    entity1.setAttributeName("推送增项-安装");
                    entity1.setAttributeValue("0");
                    entity1.setAttribute("ca");
                    worderInformationAttributeDao.insert(entity1);

                    WorderInformationAttributeEntity entity2 = new WorderInformationAttributeEntity();
                    entity2.setWorderId(worderInformationEntity.getWorderId());
                    entity2.setAttributeCode("push_order_complete");
                    entity2.setAttributeName("推送订单完成");
                    entity2.setAttributeValue("0");
                    entity2.setAttribute("ca");
                    worderInformationAttributeDao.insert(entity2);

                    WorderInformationAttributeEntity attributeEntity3 = new WorderInformationAttributeEntity();
                    attributeEntity3.setWorderId(worderInformationEntity.getWorderId());
                    attributeEntity3.setAttributeCode("push_ammeterStatus");
                    attributeEntity3.setAttributeName("推送电表安装状态");
                    attributeEntity3.setAttributeValue("0");
                    attributeEntity3.setAttribute("ca");
                    worderInformationAttributeDao.insert(attributeEntity3);

                    WorderInformationAttributeEntity attributeEntity4 = new WorderInformationAttributeEntity();
                    attributeEntity4.setWorderId(worderInformationEntity.getWorderId());
                    attributeEntity4.setAttributeCode("push_updateFirstcontactTime");
                    attributeEntity4.setAttributeName("推送首联用户时间");
                    attributeEntity4.setAttributeValue("0");
                    attributeEntity4.setAttribute("ca");
                    worderInformationAttributeDao.insert(attributeEntity4);

                    WorderInformationAttributeEntity attributeEntity5 = new WorderInformationAttributeEntity();
                    attributeEntity5.setWorderId(worderInformationEntity.getWorderId());
                    attributeEntity5.setAttributeCode("push_serviceTime");
                    attributeEntity5.setAttributeName("推送服务时间");
                    attributeEntity5.setAttributeValue("0");
                    attributeEntity5.setAttribute("ca");
                    worderInformationAttributeDao.insert(attributeEntity5);

                    WorderInformationAttributeEntity attributeEntity6 = new WorderInformationAttributeEntity();
                    attributeEntity6.setWorderId(worderInformationEntity.getWorderId());
                    attributeEntity6.setAttributeCode("push_canInstallOrders");
                    attributeEntity6.setAttributeName("推送确认安装条件-勘测");
                    attributeEntity6.setAttributeValue("0");
                    attributeEntity6.setAttribute("ca");
                    worderInformationAttributeDao.insert(attributeEntity6);

                    WorderInformationAttributeEntity attributeEntity7 = new WorderInformationAttributeEntity();
                    attributeEntity7.setWorderId(worderInformationEntity.getWorderId());
                    attributeEntity7.setAttributeCode("push_dataCheck");
                    attributeEntity7.setAttributeName("推送安装资料");
                    attributeEntity7.setAttributeValue("0");
                    attributeEntity7.setAttribute("ca");
                    worderInformationAttributeDao.insert(attributeEntity7);
                }

                if ("leapMotor".equals(worderInformation.getPushOrderWorderSource())) {
                    WorderInformationAttributeEntity entity1 = new WorderInformationAttributeEntity();
                    entity1.setWorderId(worderInformationEntity.getWorderId());
                    entity1.setAttributeCode("order_status");
                    entity1.setAttributeName("车企order状态");
                    entity1.setAttributeValue("02");
                    entity1.setAttribute("leapMotor");
                    worderInformationAttributeDao.insert(entity1);
                }
            }

            // 场景匹配
            Map<String, String> sceneRuleMap = new LinkedHashMap<>();
            if (template != null && StringUtils.isNotBlank(template.getBrandId())) {
                sceneRuleMap.put(SceneConstant.Rule.brand.getName(), template.getBrandId());
            }
            sceneRuleMap.put(SceneConstant.Rule.worderTemplate.getName(), worderInformationEntity.getTemplateId() + "");
            sceneRuleMap.put(SceneConstant.Rule.worderType.getName(), worderInformationEntity.getWorderTypeId() + "");
            worderSceneService.matchScene(worderInformationEntity.getWorderId(), sceneRuleMap);
        } catch (Exception e) {
            log.error("推送工单入库异常", e);
            return R.error("推送工单字段信息出错");
        }
        return R.ok().put("worderNo", no).put("worderId", worderId);
    }

    @Lock4j(keys = {"#worderInformation.companyOrderNumber"})
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public WorderInfoEntity saveWorderInfoEntity(WorderInfoEntity worderInformation) {
        boolean b = this.validateCompanyOrderNumberAndBrandExsit(worderInformation.getCompanyOrderNumber(), worderInformation.getTemplateId());
        if (b) {
            throw new RRException("车企订单号已存在，无法创建工单");
        }
        WorderInfoEntity worderInformationEntity = handleRedundant(worderInformation);
        log.info("生成工单 工单信息{}", JSON.toJSONString(worderInformationEntity));
        baseMapper.addWorderInfo(worderInformationEntity);
        return worderInformationEntity;
    }

    /**
     * 增加工单信息
     *
     * @param worderInformation
     */
    @Override
    @Transactional
    public R importBatchSaveWorderInformation(WorderInfoEntity worderInformation) {
        String[] regionIds = worderInformation.getAddress().split("_");
        String province = bizRegionMapper.areaName(Integer.parseInt(regionIds[0]));   //省简称
        String city = bizRegionMapper.areaName(Integer.parseInt(regionIds[1]));   //市简称
        String areaId = regionIds[2];
        String cityId = regionIds[1];
        //校验市是否属于该省
        List<BizRegion> bizRegionList = bizRegionMapper.selectArealist(Integer.parseInt(regionIds[0]));
        BizRegion region1 = bizRegionList.stream().filter(b -> b.getId().equals(Integer.parseInt(cityId))).findFirst().orElse(null);
        if (region1 == null) {
            return R.error("市编码与省编码不匹配");
        }
        //校验区是否属于该市
        List<BizRegion> bizRegions = bizRegionMapper.selectArealist(Integer.parseInt(regionIds[1]));
        BizRegion region = bizRegions.stream().filter(b -> b.getId().equals(Integer.parseInt(areaId))).findFirst().orElse(null);
        if (region == null) {
            return R.error("区编码与市编码不匹配");
        }
        String abbreviation = province + city;
        String no = worderNoMaker(abbreviation);
        //添加操作记录
        OperationRecord operationRecord = new OperationRecord();
        operationRecord.setUserId(worderInformation.getCreateBy());
        operationRecord.setOperationUser(worderInformation.getCandidate());
        Integer worderTypeId = worderInformation.getWorderTypeId();  //工单类型id
        String worderTypeName = worderTypeService.getById(worderTypeId).getName(); //工单类型名称
        String record = worderInformation.getCandidate() + "创建" + worderTypeName + "工单";
        operationRecord.setRecord(record);
        operationRecord.setType(1);
        operationRecord.setWorderNo(no);
        operationRecord.setWorderStatus(Constant.STRING_ZERO);
        operationRecord.setWorderExecStatus(Constant.STRING_ZERO);
        workMsgDao.insertOperation(operationRecord);
        //添加工单信息
        /*if(worderInformation.getTemplateId() != null){
			BrandPo brand = baseMapper.getBrand(worderInformation.getTemplateId());
			worderInformation.setCarBrand(brand.getBrandName());  //品牌名称
		}*/

        worderInformation.setWorderNo(no);
        worderInformation.setWorderStatus(7);
        worderInformation.setWorderStatusValue("工单预创建");
        worderInformation.setWorderExecStatus(0);
        worderInformation.setWorderExecStatusValue("未派单");

        worderInformation.setAreaId(Integer.parseInt(regionIds[2]));  //添加区域id
        if (StringUtils.isNotBlank(worderInformation.getUserPhone()) && worderInformation.getUserPhone().length() >= 11) {
            worderInformation.setUserPhone(worderInformation.getUserPhone().substring(0, 11));//电话
        } else {
            worderInformation.setUserPhone(worderInformation.getUserPhone());//电话
        }

        WorderInfoEntity worderInformationEntity = handleRedundant(worderInformation);
        //this.save(worderInformationEntity);
        try {
            baseMapper.addWorderInfo(worderInformationEntity);
        } catch (Exception e) {
            log.error("工单批量导入 baseMapper.addWorderInfo 异常", e);
            return R.error("导入报错");
        }
        List<WorderExtFieldEntity> worderExtFieldList = worderInformation.getWorderExtFieldList();
        try {
            if (!CollectionUtils.isEmpty(worderExtFieldList)) {
                worderExtFieldList = worderInformation.getWorderExtFieldList().stream()
                        .filter(worderExtFieldEntity -> worderExtFieldEntity != null)
                        .map(worderExtFieldEntity -> {
                            worderExtFieldEntity.setWorderNo(no);
                            worderExtFieldEntity.setCreateTime(new Date());
                            return worderExtFieldEntity;
                        })
                        .collect(Collectors.toList());
                //添加扩展字段
                worderExtFieldService.saveBatch(worderExtFieldList);
            }

            // 场景匹配
            WorderTemplateDto template = worderTemplateDao.findTemplateInfoById(worderInformationEntity.getTemplateId());
            Map<String, String> sceneRuleMap = new LinkedHashMap<>();
            if (template != null && StringUtils.isNotBlank(template.getBrandId())) {
                sceneRuleMap.put(SceneConstant.Rule.brand.getName(), template.getBrandId());
            }
            sceneRuleMap.put(SceneConstant.Rule.worderTemplate.getName(), worderInformationEntity.getTemplateId() + "");
            sceneRuleMap.put(SceneConstant.Rule.worderType.getName(), worderInformationEntity.getWorderTypeId() + "");
            worderSceneService.matchScene(worderInformationEntity.getWorderId(), sceneRuleMap);
        } catch (Exception e) {
            log.error("工单批量导入 worderExtFieldService.saveBatch 异常", e);
            return R.error("入库字段出错");
        }
        return R.ok().put("worderNo", no);
    }

    //处理区域编码
    public String handleCode(Integer areaId) {
        BizRegionEntity bizRegionEntity = bizRegionDao.selectById(areaId);
        String code = bizRegionEntity.getRegcode();
        String regcode = "(" + code.substring(0, 3);
        if (code.length() > 3) {
            regcode += ", ";
            regcode += code.substring(0, 6);
        }
        if (code.length() > 6) {
            regcode += ", ";
            regcode += code.substring(0, 9);
        }
        regcode += ")";
        return regcode;
    }

    /**
     * 分配工单给客服
     */
    public Map<String, Object> distributeWorder(Integer templateId, Integer areaId, Integer serviceType) {
        Map<String, Object> resultMap = new HashMap<>();
        BrandPo brand = baseMapper.getBrand(templateId);  //获取工单的品牌
        String regcode = handleCode(areaId);
        Long custormerId = null;
        List<SysUserEntity> userList = new ArrayList<>();
        List<CustormerWorderNumDto> numList = new ArrayList<>();
        //根据品牌和工单区域查询对应的客服
        userList = baseMapper.getCustormerWorderNumByArea(regcode, brand.getBrandId(), serviceType);
        if (userList.isEmpty() || userList == null) {
            numList = baseMapper.getCustormerWorderNum(brand.getBrandId());
            if (numList != null && !numList.isEmpty()) {
                custormerId = numList.get(0).getCustormerId(); //客服
                baseMapper.updateWorderNum(numList.get(0));
            } else {
                SysUserEntity user = getUser();
                custormerId = user.getUserId(); //客服
            }
        } else {
            if (userList.size() > 1) {
                List<String> names = new ArrayList<>();
                userList.forEach(item -> {
                    if (com.youngking.lenmoncore.common.utils.StringUtils.isNotBlank(item.getEmployeeName())) {
                        names.add(item.getEmployeeName());
                    } else {
                        names.add(item.getUserId().toString());
                    }
                });
                resultMap.put("msg", names.stream().collect(Collectors.joining(",")));
                custormerId = -1L;
            } else {
                custormerId = userList.get(0).getUserId();
            }
        }

//        SysUserEntity user = getUser();
//        CustormerWorderNumDto num = baseMapper.getNumBycustormerId(user.getUserId(), brand.getBrandId());
//        Long custormerId = user.getUserId(); //客服
//        if (num == null) {
//            //该客服第一次被分配工单
//            CustormerWorderNumDto custormerWorderNumDto = new CustormerWorderNumDto();
//            //worderInfoEntity.setCreateBy(user.getUserId()); //客服
//            custormerWorderNumDto.setCustormerId(user.getUserId());
//            custormerWorderNumDto.setBrandId(Integer.parseInt(brand.getBrandId()));
//            baseMapper.insertWorderNum(custormerWorderNumDto);
//        } else if (num != null) {
//            List<CustormerWorderNumDto> numList = baseMapper.getCustormerWorderNum(brand.getBrandId());
//            if (numList.size() == 1) {
//                //worderInfoEntity.setCreateBy(numList.get(0).getCustormerId()); //客服
//                custormerId = numList.get(0).getCustormerId(); //客服
//                baseMapper.updateWorderNum(numList.get(0));
//            } else if (numList.size() > 1) {
//                //随机取一个客服分配给他
//                Random random = new Random();
//                int index = random.nextInt(numList.size());
//                CustormerWorderNumDto custormerWorderNumDto = numList.get(index);
//                //worderInfoEntity.setCreateBy(custormerWorderNumDto.getCustormerId());
//                custormerId = custormerWorderNumDto.getCustormerId(); //客服
//                //更新客服处理的工单数量
//                baseMapper.updateWorderNum(custormerWorderNumDto);
//            }
//        }
        resultMap.put("userId", custormerId);
        return resultMap;
    }


    /**
     * 处理地址冗余字段
     *
     * @return
     */
    @Override
    public WorderInfoEntity handleRedundant(WorderInfoEntity worderInformation) {
        String address = worderInformation.getAddress();
        if (StringUtils.isNotEmpty(address)) {
            String[] regionIds = worderInformation.getAddress().split("_");
            String provinceName = workMsgDao.selectAreaName(Integer.parseInt(regionIds[0]));  //省名称
            String cityName = workMsgDao.selectAreaName(Integer.parseInt(regionIds[1]));    //市名称
            String areaName = workMsgDao.selectAreaName(Integer.parseInt(regionIds[2]));    //区域名称
            //地区冗余值
            worderInformation.setAreaId(Integer.parseInt(regionIds[2]));
            if (regionIds.length < 4) {
                worderInformation.setAddressDup(provinceName + "_" + cityName + "_" + areaName);
            } else {
                worderInformation.setAddressDup(provinceName + "_" + cityName + "_" + areaName + "_" + regionIds[3]);
            }
        }
        return worderInformation;
    }


    /**
     * 更新工单信息
     *
     * @param worderInformation
     */
    @Override
    @Transactional
    public R updateWorderInformation(WorderInfoEntity worderInformation) {
        List<WorderInformationEntity> list = baseMapper.queryCompanyOrderNumber(worderInformation.getCompanyOrderNumber(), worderInformation.getWorderNo());
        if (list.size() > 0) {
            //有重复车企订单号

            return R.error("该车企订单号已存在，无法修改工单");
        }
        worderInformation.setUpdateBy(getUser().getUserId());
        //this.updateById(worderInformation);
        WorderInfoEntity worderInfoEntity = handleRedundant(worderInformation);
        List<WorderExtFieldEntity> worderExtFieldList = worderInformation.getWorderExtFieldList();
        for (WorderExtFieldEntity worderExtFieldEntity : worderExtFieldList) {
            if (worderExtFieldEntity.getFieldName().equals("卡泰驰SN编码")) {
                if (worderExtFieldEntity.getFieldValue().length() == 24) {
                    String str = worderExtFieldEntity.getFieldValue().substring(0, 4);
                    for (WorderExtFieldEntity extFieldEntity : worderExtFieldList) {
                        if (extFieldEntity.getFieldName().equals("卡泰驰厂商")) {
                            String name = baseMapper.getFactoryName(str);
                            extFieldEntity.setFieldValue(name);
                        }
                    }
                    break;
                } else {
                    return R.error("卡泰驰SN编码长度应为24位，请检查后再提交。");
                }
            }
        }
        baseMapper.updateWorderInfo(worderInfoEntity);


        if (!CollectionUtils.isEmpty(worderExtFieldList)) {
            worderExtFieldList.forEach(worderExtFieldEntity -> {
                worderExtFieldEntity.setWorderNo(worderInformation.getWorderNo());
                baseMapper.updateWorderExtField(worderExtFieldEntity);
            });
        }
        return R.ok();

    }

    /**
     * 处理冗余字段值
     *
     * @param page
     * @return
     */
    public PageUtils handleValue(PageUtils page, List<SysRoleEntity> list) {

        List<WorderInfoEntity> worderInfolist = (List<WorderInfoEntity>) page.getList();
        List<WorderInfoEntity> worderInfoEntityList = worderInfolist.stream().map(worderInfo -> {
            if (StringUtils.isEmpty(worderInfo.getCarBrand())) {
                //品牌名称
                worderInfo.setCarBrand(baseMapper.getBrand(worderInfo.getTemplateId()).getBrandName());
            }
            Integer worderExecStatus = worderInfo.getWorderExecStatus(); //工单执行状态
            if (worderExecStatus != null) {
                List<ExtFieldDictionaryDto> worderExecStatusList = extFieldDao.findByDicNumber("worder_exec_status");
                worderExecStatusList.forEach(execStatus -> {
                    if (worderExecStatus.toString().equals(execStatus.getDetailNumber())) {
                        worderInfo.setWorderExecStatusValue(execStatus.getDetailName());
                    }
                });
            }
            List<BillingRecodeDTO> billingRecords = billingRecodeMapper.getBillingRecordByWorderNo(worderInfo.getWorderNo());
            if (billingRecords.size() > IntegerEnum.ZERO.getValue()) {
                worderInfo.setBillingRecodeDTO(billingRecords.get(IntegerEnum.ZERO.getValue()));
            }

            return worderInfo;
        }).collect(Collectors.toList());

        // 设置当前工单的转单标识
        setTransferOrder(worderInfoEntityList);
        // 暂停标识
        setSusPendOrder(worderInfoEntityList);
        // 判断售后类型
        setWorderTypeAfterSales(worderInfoEntityList);

        page.setList(worderInfoEntityList);
        return page;
    }

    /**
     * 查询工单列表
     *
     * @param params
     * @return
     */
    @Override
    public PageUtils queryList(Map<String, Object> params) {
        //取出前端传过来的userId
        //Long userId = Long.parseLong(params.get("userId").toString());
        Long userId = getUser().getUserId();
        //获取用户角色信息
        List<SysRoleEntity> list = sysRoleService.getByUserId(userId);
        //添加每个客服只能看到自己创建的工单
        if (!com.youngking.lenmoncore.common.utils.StringUtils.isEmpty(params.get("regionLabelList"))) {
            String regionLabelList = Objects.toString(params.get("regionLabelList"));
            String[] areas = regionLabelList.split(",");
            List<String> areaId = new ArrayList<>();
            List<String> area = new ArrayList<>();
            for (int i = 0; i < areas.length; i++) {
                QueryWrapper<BizRegionEntity> query = new QueryWrapper<>();
                String id = areas[i];
                query.eq("id", id);
                BizRegionEntity bizRegionEntity = bizRegionDao.selectOne(query);
                if (bizRegionEntity.getType() == 1) {
                    //省
                    area.add(bizRegionEntity.getId().toString());
                } else if (bizRegionEntity.getType() == 2) {
                    //市
                    QueryWrapper<BizRegionEntity> queryWrapper = new QueryWrapper<>();
                    queryWrapper.eq("pid", bizRegionEntity.getId());
                    List<BizRegionEntity> bizRegionEntities = bizRegionDao.selectList(queryWrapper);
                    for (int j = 0; j < bizRegionEntities.size(); j++) {
                        areaId.add(bizRegionEntities.get(j).getId().toString());
                    }
                } else if (bizRegionEntity.getType() == 3) {
                    //县
                    areaId.add(bizRegionEntity.getId().toString());
                }
            }

            if (area != null && area.size() > 0) {
                params.put("areas", area);
            }
            if (areaId != null && areaId.size() > 0) {
                params.put("areaIds", areaId);
            }
        }

        if (!com.youngking.lenmoncore.common.utils.StringUtils.isEmpty(params.get("carBrands"))) {
            String carBrands = Objects.toString(params.get("carBrands"));
            String[] carBrandArr = carBrands.split(",");
            params.put("carBrands", Arrays.asList(carBrandArr));
        }

        Set<String> keys = new HashSet<>(params.keySet());
        for (String k : keys) {
            Object obj = params.get(k);
            if (obj != null && StringUtils.isBlank(obj + "")) {
                params.remove(k);
            }
        }
        PageUtils page = new PageUtils();
        if (UserRoleUtils.isAdmin(list)) {
            page = queryPage(params);
            if (!CollectionUtils.isEmpty(page.getList())) {
                page = handleValue(page, list);
            }
        } else if (UserRoleUtils.isCustomerService(list) || UserRoleUtils.isCompanyManager(list)) {//客服或者厂商管理员
            params.put("createBy", userId);
            page = queryPageCustomer(params);
            if (!CollectionUtils.isEmpty(page.getList())) {
                page = handleValue(page, list);
            }
        } else if (UserRoleUtils.isPM(list)) {//项目经理
            params.put("pmId", userId);
            page = queryPagePM(params);
            if (!CollectionUtils.isEmpty(page.getList())) {
                page = handleValue(page, list);
            }
        } else if (UserRoleUtils.isBranch(list)) {//网点
            params.put("dotId", getDotId(userId));
            page = queryPageBranch(params);
            if (!CollectionUtils.isEmpty(page.getList())) {
                page = handleValue(page, list);
            }
        } else if (UserRoleUtils.isService(list)) {//服务兵
            params.put("serviceId", userId);
            page = queryService(params);
            if (!CollectionUtils.isEmpty(page.getList())) {
                page = handleValue(page, list);
            }
        } else {
            params.put("userId", userId);
            page = queryAreaBrandPage(params);
            if (!CollectionUtils.isEmpty(page.getList())) {
                page = handleValue(page, list);
            }
        }
        List<WorderInfoEntity> worderInformationEntities = (List<WorderInfoEntity>) page.getList();
        worderInformationEntities.forEach(item -> {
            // 查询当前工单用户增项收费状态
            item.setWorderPayStatus(worderPayService.getPayStatusByWorderNo(item.getWorderNo()));
        });

        List<WorderInfoEntity> entityList = (List<WorderInfoEntity>) page.getList();
        for (WorderInfoEntity worderInfoEntity : entityList) {
            Integer superviseNum = baseMapper.querySuperviseByWorderNo(worderInfoEntity.getWorderNo());
            worderInfoEntity.setSuperviseNum(superviseNum);
        }
        setTslPresurvey(worderInformationEntities);
        // 设置当前工单的转单标识
        setTransferOrder(worderInformationEntities);
        return page;
    }

    /**
     * 设置工单是否存在转单的属性
     *
     * @param worderInformationEntities
     */
    private void setTransferOrder(List<WorderInfoEntity> worderInformationEntities) {
        if (worderInformationEntities == null || worderInformationEntities.isEmpty()) {
            return;
        }
        // 获取工单ID集合
        List<Integer> worderIds = worderInformationEntities.stream().map(WorderInfoEntity::getWorderId).collect(Collectors.toList());
        LambdaQueryWrapper<WorderInformationAttributeEntity> worderAttributeWrapper = Wrappers.lambdaQuery();
        // 查询工单对应的属性表是否存在转单标识
        worderAttributeWrapper
                .in(WorderInformationAttributeEntity::getWorderId, worderIds)
                .eq(WorderInformationAttributeEntity::getIsDelete, 0)
                .eq(WorderInformationAttributeEntity::getAttributeCode, "Transfer-Order")
                .eq(WorderInformationAttributeEntity::getAttribute, "TransferOrder");
        List<WorderInformationAttributeEntity> worderInformationAttributes = worderInformationAttributeDao.selectList(worderAttributeWrapper);
        if (worderInformationAttributes != null && !worderInformationAttributes.isEmpty()) {
            Map<Integer, String> attrMap = worderInformationAttributes.stream().collect(Collectors.toMap(WorderInformationAttributeEntity::getWorderId, WorderInformationAttributeEntity::getAttributeValue, (val1, val2) -> val1));
            worderInformationEntities.forEach(item -> {
                if (attrMap.containsKey(item.getWorderId()) && "1".equals(attrMap.get(item.getWorderId()))) {
                    item.setTransferOrder("已转单");
                }
            });
        }
    }

    /**
     * 设置工单是否存在暂停的属性
     *
     * @param worderInformationEntities
     */
    private void setSusPendOrder(List<WorderInfoEntity> worderInformationEntities) {
        if (worderInformationEntities == null || worderInformationEntities.isEmpty()) {
            return;
        }
        // 获取工单ID集合
        List<Integer> worderIds = worderInformationEntities.stream().map(WorderInfoEntity::getWorderId).collect(Collectors.toList());
        LambdaQueryWrapper<WorderInformationAttributeEntity> worderAttributeWrapper = Wrappers.lambdaQuery();
        // 查询工单对应的属性表是否存在转单标识
        worderAttributeWrapper
                .in(WorderInformationAttributeEntity::getWorderId, worderIds)
                .eq(WorderInformationAttributeEntity::getAttributeCode, "Suspend-flag")
                .eq(WorderInformationAttributeEntity::getAttribute, "Suspend")
                .eq(WorderInformationAttributeEntity::getAttributeValue, "1")
                .eq(WorderInformationAttributeEntity::getIsDelete, 0);
        List<WorderInformationAttributeEntity> worderInformationAttributes = worderInformationAttributeDao.selectList(worderAttributeWrapper);
        if (worderInformationAttributes != null && !worderInformationAttributes.isEmpty()) {
            Map<Integer, String> attrMap = worderInformationAttributes.stream().collect(Collectors.toMap(WorderInformationAttributeEntity::getWorderId, WorderInformationAttributeEntity::getAttributeValue, (val1, val2) -> val1));
            worderInformationEntities.forEach(item -> {
                if (attrMap.containsKey(item.getWorderId()) && "1".equals(attrMap.get(item.getWorderId()))) {
                    item.setSusPendOrder("已暂停");
                }
            });
        }
    }

    /**
     * 设置工单的类型判断售后类型
     *
     * @param worderInformationEntities
     */
    private void setWorderTypeAfterSales(List<WorderInfoEntity> worderInformationEntities) {
        if (worderInformationEntities == null || worderInformationEntities.isEmpty()) {
            return;
        }
        // 获取工单ID集合
        List<String> worderNos = worderInformationEntities.stream().map(WorderInfoEntity::getWorderNo).collect(Collectors.toList());
        LambdaQueryWrapper<WorderExtFieldEntity> worderExtFieldWrapper = Wrappers.lambdaQuery();
        // 查询工单对应的属性表是否存在转单标识
        worderExtFieldWrapper
                .in(WorderExtFieldEntity::getWorderNo, worderNos)
                .eq(WorderExtFieldEntity::getFieldId, 1697);
        List<WorderExtFieldEntity> worderExtFieldEntities = worderExtFieldDao.selectList(worderExtFieldWrapper);
        if (worderExtFieldEntities != null && !worderExtFieldEntities.isEmpty()) {
            Map<String, String> attrMap = new HashMap<>();
            worderExtFieldEntities.forEach(item -> attrMap.put(item.getWorderNo(), item.getFieldValue()));
            worderInformationEntities.forEach(item -> {
                if (attrMap.containsKey(item.getWorderNo()) && StringUtils.isNotBlank(attrMap.get(item.getWorderNo()))) {
                    item.setWorderTypeName(item.getWorderTypeName() + "（" + attrMap.get(item.getWorderNo()) + "）");
                }
            });
        }
    }

    private void setTslPresurvey(List<WorderInfoEntity> worderInformationEntities) {
        if (worderInformationEntities == null || worderInformationEntities.isEmpty()) {
            return;
        }
        List<String> worderNos = worderInformationEntities.stream().map(WorderInfoEntity::getWorderNo).collect(Collectors.toList());
        LambdaQueryWrapper<WorderExtFieldEntity> worderExtFieldWrapper = Wrappers.lambdaQuery();
        worderExtFieldWrapper
                .in(WorderExtFieldEntity::getWorderNo, worderNos)
                .eq(WorderExtFieldEntity::getFieldId, 1224)
                .eq(WorderExtFieldEntity::getFieldValue, "否");
        List<WorderExtFieldEntity> worderExtFieldEntities = worderExtFieldDao.selectList(worderExtFieldWrapper);
        if (worderExtFieldEntities != null && !worderExtFieldEntities.isEmpty()) {
            List<String> presurveyWorderNos = worderExtFieldEntities.stream().map(WorderExtFieldEntity::getWorderNo).collect(Collectors.toList());
            worderInformationEntities.forEach(item -> {
                if ("特斯拉".equals(item.getCarBrand()) && presurveyWorderNos.contains(item.getWorderNo())) {
                    item.setTslPresurvey(true);
                }
            });
        }
    }


    @Override
    public ColorLabelCount getColorLabelCount() {
        Long userId = getUser().getUserId();
        //获取用户角色信息
        List<SysRoleEntity> list = sysRoleService.getByUserId(userId);
        MapUtils mapUtils = new MapUtils().put("currUserId", userId);
        PageUtils page = new PageUtils();
        if (UserRoleUtils.isCustomerService(list) || UserRoleUtils.isCompanyManager(list)) {//客服或者厂商管理员
            mapUtils.put("worderStatusIds", "(0,7,8,15,16)");
        } else if (UserRoleUtils.isPM(list)) {//项目经理
            mapUtils.put("worderStatusIds", "(5,13,18)");
        } else if (UserRoleUtils.isBranch(list)) {//网点
            mapUtils.put("worderStatusIds", "(1,2,6,9,10,14)");
        } else {
            mapUtils.put("worderStatusIds", "(0,1,2,5,6,7,8,10,13,14,15,16,18)");
        }
        mapUtils.put("worderStatus", 0);
        // 分配中列表色标数量
        Integer allocationColorLabelCount = baseMapper.getColorLabelCount(mapUtils);
        mapUtils.put("worderStatus", 1);
        // 勘测中列表色标数量
        Integer conveyColorLabelCount = baseMapper.getColorLabelCount(mapUtils);
        mapUtils.put("worderStatus", 2);
        // 安装中列表色标数量
        Integer installColorLabelCount = baseMapper.getColorLabelCount(mapUtils);
        ColorLabelCount.ColorLabelCountBuilder colorLabelCountBuilder = ColorLabelCount.builder().allocationColorLabelCount(allocationColorLabelCount).conveyColorLabelCount(conveyColorLabelCount).installColorLabelCount(installColorLabelCount);
        ColorLabelCount colorLabelCount = colorLabelCountBuilder.build();
        return colorLabelCount;
    }

    /**
     * 查询所有工单数据
     */
    @Override
    public PageUtils queryAll(Map<String, Object> params) {
        if (!com.youngking.lenmoncore.common.utils.StringUtils.isEmpty(params.get("company"))) {
            Integer companyType = baseMapper.getCompanyType(String.valueOf(params.get("company")));
            if (companyType == 0) {
                params.put("companyId", params.get("company"));
            } else if (companyType == 1) {
                params.put("fzx", params.get("company"));
            } else if (companyType == 2) {
                params.put("jxs", params.get("company"));
            }
        }

        if (!com.youngking.lenmoncore.common.utils.StringUtils.isEmpty(params.get("brandIds"))) {
            String brandIds = Objects.toString(params.get("brandIds"));
            String[] brandIdArr = brandIds.split(",");
            params.put("brandIds", Arrays.asList(brandIdArr));
        }

        Set<String> keys = new HashSet<>(params.keySet());
        for (String k : keys) {
            Object obj = params.get(k);
            if (obj != null && StringUtils.isBlank(obj + "")) {
                params.remove(k);
            }
        }

        Long userId = getUser().getUserId();
        List<SysRoleEntity> list = sysRoleService.getByUserId(userId);

        if (!com.youngking.lenmoncore.common.utils.StringUtils.isEmpty(params.get("regionLabelList"))) {
            String regionLabelList = Objects.toString(params.get("regionLabelList"));
            String[] areas = regionLabelList.split(",");
            List<String> areaId = new ArrayList<>();
            List<String> area = new ArrayList<>();
            for (int i = 0; i < areas.length; i++) {
                QueryWrapper<BizRegionEntity> query = new QueryWrapper<>();
                String id = areas[i];
                query.eq("id", id);
                BizRegionEntity bizRegionEntity = bizRegionDao.selectOne(query);
                if (bizRegionEntity.getType() == 1) {
                    //省
                    area.add(bizRegionEntity.getId().toString());
                } else if (bizRegionEntity.getType() == 2) {
                    //市
                    QueryWrapper<BizRegionEntity> queryWrapper = new QueryWrapper<>();
                    queryWrapper.eq("pid", bizRegionEntity.getId());
                    List<BizRegionEntity> bizRegionEntities = bizRegionDao.selectList(queryWrapper);
                    for (int j = 0; j < bizRegionEntities.size(); j++) {
                        areaId.add(bizRegionEntities.get(j).getId().toString());
                    }
                } else if (bizRegionEntity.getType() == 3) {
                    //县
                    areaId.add(bizRegionEntity.getId().toString());
                }
            }

            if (area != null && area.size() > 0) {
                params.put("areas", area);
            }
            if (areaId != null && areaId.size() > 0) {
                params.put("areaIds", areaId);
            }
        }
        SysUserEntity user = getUser();
        List<ManagerAreaBrandDto> userAreaBrand = baseMapper.getUserAreaBrand(user.getUserId());
        if (!CollectionUtils.isEmpty(userAreaBrand)) {
            List<Integer> areaIds = userAreaBrand.stream().filter(o -> o.getAreaId() != null).map(ManagerAreaBrandDto::getAreaId).collect(Collectors.toList());
            List<Integer> brandIds = userAreaBrand.stream().filter(o -> o.getBrandId() != null).map(ManagerAreaBrandDto::getBrandId).collect(Collectors.toList());
            params.put("userId", user.getUserId());
            if (!CollectionUtils.isEmpty(areaIds)) {
                params.put("areaFlag", 1);
                if (!CollectionUtils.isEmpty(brandIds)) {
                    params.put("brandFlag", 1);
                }
            } else if (!CollectionUtils.isEmpty(brandIds)) {
                params.put("brandFlag", 1);
            }
        }

        if (UserRoleUtils.isCustomerService(list) || UserRoleUtils.isCompanyManager(list)) {//客服或者厂商管理员
            params.put("createBy", userId);
        } else if (UserRoleUtils.isPM(list)) {//项目经理
            params.put("pmId", userId);
        } else if (UserRoleUtils.isBranch(list)) {//网点
            Integer dotId = getDotId(userId);
            params.put("dotId", dotId != null ? dotId : -1);
            params.put("brandFlag", null);
            params.put("areaFlag", null);
        } else if (UserRoleUtils.isService(list)) {//服务兵
            LambdaQueryWrapper<BizAttendantEntity> bizAttendantWrapper = Wrappers.lambdaQuery();
            bizAttendantWrapper.eq(BizAttendantEntity::getUserId, userId);
            List<BizAttendantEntity> bizAttendantEntities = bizAttendantDao.selectList(bizAttendantWrapper);
            params.put("serviceId", bizAttendantEntities.isEmpty() ? "null" : bizAttendantEntities.get(0).getId());
            params.put("brandFlag", null);
            params.put("areaFlag", null);
        }

        IPage<WorderInformationEntity> page = baseMapper.getOrderAllList(new Query<WorderInformationEntity>().getPageNotI(params), params);
        PageUtils pageUtils = handleValue(new PageUtils(page), new ArrayList<>());

        return pageUtils;
    }

    @Override
    public PageUtils companyCancelAuditQueryAll(Map<String, Object> params) {
        if (!com.youngking.lenmoncore.common.utils.StringUtils.isEmpty(params.get("company"))) {
            Integer companyType = baseMapper.getCompanyType(String.valueOf(params.get("company")));
            if (companyType == 0) {
                params.put("companyId", params.get("company"));
            } else if (companyType == 1) {
                params.put("fzx", params.get("company"));
            } else if (companyType == 2) {
                params.put("jxs", params.get("company"));
            }
        }
        Long userId = getUser().getUserId();
        List<SysRoleEntity> list = sysRoleService.getByUserId(userId);

        if (!com.youngking.lenmoncore.common.utils.StringUtils.isEmpty(params.get("regionLabelList"))) {
            String regionLabelList = Objects.toString(params.get("regionLabelList"));
            String[] areas = regionLabelList.split(",");
            List<String> areaId = new ArrayList<>();
            List<String> area = new ArrayList<>();
            for (int i = 0; i < areas.length; i++) {
                QueryWrapper<BizRegionEntity> query = new QueryWrapper<>();
                String id = areas[i];
                query.eq("id", id);
                BizRegionEntity bizRegionEntity = bizRegionDao.selectOne(query);
                if (bizRegionEntity.getType() == 1) {
                    //省
                    area.add(bizRegionEntity.getId().toString());
                } else if (bizRegionEntity.getType() == 2) {
                    //市
                    QueryWrapper<BizRegionEntity> queryWrapper = new QueryWrapper<>();
                    queryWrapper.eq("pid", bizRegionEntity.getId());
                    List<BizRegionEntity> bizRegionEntities = bizRegionDao.selectList(queryWrapper);
                    for (int j = 0; j < bizRegionEntities.size(); j++) {
                        areaId.add(bizRegionEntities.get(j).getId().toString());
                    }
                } else if (bizRegionEntity.getType() == 3) {
                    //县
                    areaId.add(bizRegionEntity.getId().toString());
                }
            }

            if (area != null && area.size() > 0) {
                params.put("areas", area);
            }
            if (areaId != null && areaId.size() > 0) {
                params.put("areaIds", areaId);
            }
        }
        SysUserEntity user = getUser();
        List<ManagerAreaBrandDto> userAreaBrand = baseMapper.getUserAreaBrand(user.getUserId());
        if (!CollectionUtils.isEmpty(userAreaBrand)) {
            List<Integer> areaIds = userAreaBrand.stream().filter(o -> o.getAreaId() != null).map(ManagerAreaBrandDto::getAreaId).collect(Collectors.toList());
            List<Integer> brandIds = userAreaBrand.stream().filter(o -> o.getBrandId() != null).map(ManagerAreaBrandDto::getBrandId).collect(Collectors.toList());
            params.put("userId", user.getUserId());
            if (!CollectionUtils.isEmpty(areaIds)) {
                params.put("areaFlag", 1);
                if (!CollectionUtils.isEmpty(brandIds)) {
                    params.put("brandFlag", 1);
                }
            } else if (!CollectionUtils.isEmpty(brandIds)) {
                params.put("brandFlag", 1);
            }
        }

        if (UserRoleUtils.isBranch(list)) {//网点
            Integer dotId = getDotId(userId);
            params.put("dotId", dotId != null ? dotId : -1);
            params.put("brandFlag", null);
            params.put("areaFlag", null);
        } else if (UserRoleUtils.isService(list)) {//服务兵
            LambdaQueryWrapper<BizAttendantEntity> bizAttendantWrapper = Wrappers.lambdaQuery();
            bizAttendantWrapper.eq(BizAttendantEntity::getUserId, userId);
            List<BizAttendantEntity> bizAttendantEntities = bizAttendantDao.selectList(bizAttendantWrapper);
            params.put("serviceId", bizAttendantEntities.isEmpty() ? "null" : bizAttendantEntities.get(0).getId());
            params.put("brandFlag", null);
            params.put("areaFlag", null);
        }

        IPage<WorderInformationEntity> page = baseMapper.companyCancelAuditQueryAll(new Query<WorderInformationEntity>().getPageNotI(params), params);
        PageUtils pageUtils = handleValue(new PageUtils(page), new ArrayList<>());

        return pageUtils;
    }

    @Override
    public PageUtils companyCancelAuditCcQueryAll(Map<String, Object> params) {
        if (!com.youngking.lenmoncore.common.utils.StringUtils.isEmpty(params.get("company"))) {
            Integer companyType = baseMapper.getCompanyType(String.valueOf(params.get("company")));
            if (companyType == 0) {
                params.put("companyId", params.get("company"));
            } else if (companyType == 1) {
                params.put("fzx", params.get("company"));
            } else if (companyType == 2) {
                params.put("jxs", params.get("company"));
            }
        }
        Long userId = getUser().getUserId();
        List<SysRoleEntity> list = sysRoleService.getByUserId(userId);

        if (!com.youngking.lenmoncore.common.utils.StringUtils.isEmpty(params.get("regionLabelList"))) {
            String regionLabelList = Objects.toString(params.get("regionLabelList"));
            String[] areas = regionLabelList.split(",");
            List<String> areaId = new ArrayList<>();
            List<String> area = new ArrayList<>();
            for (int i = 0; i < areas.length; i++) {
                QueryWrapper<BizRegionEntity> query = new QueryWrapper<>();
                String id = areas[i];
                query.eq("id", id);
                BizRegionEntity bizRegionEntity = bizRegionDao.selectOne(query);
                if (bizRegionEntity.getType() == 1) {
                    //省
                    area.add(bizRegionEntity.getId().toString());
                } else if (bizRegionEntity.getType() == 2) {
                    //市
                    QueryWrapper<BizRegionEntity> queryWrapper = new QueryWrapper<>();
                    queryWrapper.eq("pid", bizRegionEntity.getId());
                    List<BizRegionEntity> bizRegionEntities = bizRegionDao.selectList(queryWrapper);
                    for (int j = 0; j < bizRegionEntities.size(); j++) {
                        areaId.add(bizRegionEntities.get(j).getId().toString());
                    }
                } else if (bizRegionEntity.getType() == 3) {
                    //县
                    areaId.add(bizRegionEntity.getId().toString());
                }
            }

            if (area != null && area.size() > 0) {
                params.put("areas", area);
            }
            if (areaId != null && areaId.size() > 0) {
                params.put("areaIds", areaId);
            }
        }
        SysUserEntity user = getUser();
        List<ManagerAreaBrandDto> userAreaBrand = baseMapper.getUserAreaBrand(user.getUserId());
        if (!CollectionUtils.isEmpty(userAreaBrand)) {
            List<Integer> areaIds = userAreaBrand.stream().filter(o -> o.getAreaId() != null).map(ManagerAreaBrandDto::getAreaId).collect(Collectors.toList());
            List<Integer> brandIds = userAreaBrand.stream().filter(o -> o.getBrandId() != null).map(ManagerAreaBrandDto::getBrandId).collect(Collectors.toList());
            params.put("userId", user.getUserId());
            if (!CollectionUtils.isEmpty(areaIds)) {
                params.put("areaFlag", 1);
                if (!CollectionUtils.isEmpty(brandIds)) {
                    params.put("brandFlag", 1);
                }
            } else if (!CollectionUtils.isEmpty(brandIds)) {
                params.put("brandFlag", 1);
            }
        }

        if (UserRoleUtils.isBranch(list)) {//网点
            Integer dotId = getDotId(userId);
            params.put("dotId", dotId != null ? dotId : -1);
            params.put("brandFlag", null);
            params.put("areaFlag", null);
        } else if (UserRoleUtils.isService(list)) {//服务兵
            LambdaQueryWrapper<BizAttendantEntity> bizAttendantWrapper = Wrappers.lambdaQuery();
            bizAttendantWrapper.eq(BizAttendantEntity::getUserId, userId);
            List<BizAttendantEntity> bizAttendantEntities = bizAttendantDao.selectList(bizAttendantWrapper);
            params.put("serviceId", bizAttendantEntities.isEmpty() ? "null" : bizAttendantEntities.get(0).getId());
            params.put("brandFlag", null);
            params.put("areaFlag", null);
        }

        IPage<WorderInformationEntity> page = baseMapper.companyCancelAuditCcQueryAll(new Query<WorderInformationEntity>().getPageNotI(params), params);
        PageUtils pageUtils = handleValue(new PageUtils(page), new ArrayList<>());

        return pageUtils;
    }

    @Override
    public PageUtils companyCancelAuditReminderQueryAll(Map<String, Object> params) {
        if (!com.youngking.lenmoncore.common.utils.StringUtils.isEmpty(params.get("company"))) {
            Integer companyType = baseMapper.getCompanyType(String.valueOf(params.get("company")));
            if (companyType == 0) {
                params.put("companyId", params.get("company"));
            } else if (companyType == 1) {
                params.put("fzx", params.get("company"));
            } else if (companyType == 2) {
                params.put("jxs", params.get("company"));
            }
        }
        Long userId = getUser().getUserId();
        List<SysRoleEntity> list = sysRoleService.getByUserId(userId);

        if (!com.youngking.lenmoncore.common.utils.StringUtils.isEmpty(params.get("regionLabelList"))) {
            String regionLabelList = Objects.toString(params.get("regionLabelList"));
            String[] areas = regionLabelList.split(",");
            List<String> areaId = new ArrayList<>();
            List<String> area = new ArrayList<>();
            for (int i = 0; i < areas.length; i++) {
                QueryWrapper<BizRegionEntity> query = new QueryWrapper<>();
                String id = areas[i];
                query.eq("id", id);
                BizRegionEntity bizRegionEntity = bizRegionDao.selectOne(query);
                if (bizRegionEntity.getType() == 1) {
                    //省
                    area.add(bizRegionEntity.getId().toString());
                } else if (bizRegionEntity.getType() == 2) {
                    //市
                    QueryWrapper<BizRegionEntity> queryWrapper = new QueryWrapper<>();
                    queryWrapper.eq("pid", bizRegionEntity.getId());
                    List<BizRegionEntity> bizRegionEntities = bizRegionDao.selectList(queryWrapper);
                    for (int j = 0; j < bizRegionEntities.size(); j++) {
                        areaId.add(bizRegionEntities.get(j).getId().toString());
                    }
                } else if (bizRegionEntity.getType() == 3) {
                    //县
                    areaId.add(bizRegionEntity.getId().toString());
                }
            }

            if (area != null && area.size() > 0) {
                params.put("areas", area);
            }
            if (areaId != null && areaId.size() > 0) {
                params.put("areaIds", areaId);
            }
        }
        SysUserEntity user = getUser();
        List<ManagerAreaBrandDto> userAreaBrand = baseMapper.getUserAreaBrand(user.getUserId());
        if (!CollectionUtils.isEmpty(userAreaBrand)) {
            List<Integer> areaIds = userAreaBrand.stream().filter(o -> o.getAreaId() != null).map(ManagerAreaBrandDto::getAreaId).collect(Collectors.toList());
            List<Integer> brandIds = userAreaBrand.stream().filter(o -> o.getBrandId() != null).map(ManagerAreaBrandDto::getBrandId).collect(Collectors.toList());
            params.put("userId", user.getUserId());
            if (!CollectionUtils.isEmpty(areaIds)) {
                params.put("areaFlag", 1);
                if (!CollectionUtils.isEmpty(brandIds)) {
                    params.put("brandFlag", 1);
                }
            } else if (!CollectionUtils.isEmpty(brandIds)) {
                params.put("brandFlag", 1);
            }
        }

        if (UserRoleUtils.isBranch(list)) {//网点
            Integer dotId = getDotId(userId);
            params.put("dotId", dotId != null ? dotId : -1);
            params.put("brandFlag", null);
            params.put("areaFlag", null);
        } else if (UserRoleUtils.isService(list)) {//服务兵
            LambdaQueryWrapper<BizAttendantEntity> bizAttendantWrapper = Wrappers.lambdaQuery();
            bizAttendantWrapper.eq(BizAttendantEntity::getUserId, userId);
            List<BizAttendantEntity> bizAttendantEntities = bizAttendantDao.selectList(bizAttendantWrapper);
            params.put("serviceId", bizAttendantEntities.isEmpty() ? "null" : bizAttendantEntities.get(0).getId());
            params.put("brandFlag", null);
            params.put("areaFlag", null);
        }

        IPage<WorderInformationEntity> page = baseMapper.companyCancelAuditReminderQueryAll(new Query<WorderInformationEntity>().getPageNotI(params), params);
        PageUtils pageUtils = handleValue(new PageUtils(page), new ArrayList<>());

        return pageUtils;
    }

    @Override
    public PageUtils companyReviewFailedQueryAll(Map<String, Object> params) {
        if (!com.youngking.lenmoncore.common.utils.StringUtils.isEmpty(params.get("company"))) {
            Integer companyType = baseMapper.getCompanyType(String.valueOf(params.get("company")));
            if (companyType == 0) {
                params.put("companyId", params.get("company"));
            } else if (companyType == 1) {
                params.put("fzx", params.get("company"));
            } else if (companyType == 2) {
                params.put("jxs", params.get("company"));
            }
        }
        Long userId = getUser().getUserId();
        List<SysRoleEntity> list = sysRoleService.getByUserId(userId);

        if (!com.youngking.lenmoncore.common.utils.StringUtils.isEmpty(params.get("regionLabelList"))) {
            String regionLabelList = Objects.toString(params.get("regionLabelList"));
            String[] areas = regionLabelList.split(",");
            List<String> areaId = new ArrayList<>();
            List<String> area = new ArrayList<>();
            for (int i = 0; i < areas.length; i++) {
                QueryWrapper<BizRegionEntity> query = new QueryWrapper<>();
                String id = areas[i];
                query.eq("id", id);
                BizRegionEntity bizRegionEntity = bizRegionDao.selectOne(query);
                if (bizRegionEntity.getType() == 1) {
                    //省
                    area.add(bizRegionEntity.getId().toString());
                } else if (bizRegionEntity.getType() == 2) {
                    //市
                    QueryWrapper<BizRegionEntity> queryWrapper = new QueryWrapper<>();
                    queryWrapper.eq("pid", bizRegionEntity.getId());
                    List<BizRegionEntity> bizRegionEntities = bizRegionDao.selectList(queryWrapper);
                    for (int j = 0; j < bizRegionEntities.size(); j++) {
                        areaId.add(bizRegionEntities.get(j).getId().toString());
                    }
                } else if (bizRegionEntity.getType() == 3) {
                    //县
                    areaId.add(bizRegionEntity.getId().toString());
                }
            }

            if (area != null && area.size() > 0) {
                params.put("areas", area);
            }
            if (areaId != null && areaId.size() > 0) {
                params.put("areaIds", areaId);
            }
        }
        SysUserEntity user = getUser();
        List<ManagerAreaBrandDto> userAreaBrand = baseMapper.getUserAreaBrand(user.getUserId());
        if (!CollectionUtils.isEmpty(userAreaBrand)) {
            List<Integer> areaIds = userAreaBrand.stream().filter(o -> o.getAreaId() != null).map(ManagerAreaBrandDto::getAreaId).collect(Collectors.toList());
            List<Integer> brandIds = userAreaBrand.stream().filter(o -> o.getBrandId() != null).map(ManagerAreaBrandDto::getBrandId).collect(Collectors.toList());
            params.put("userId", user.getUserId());
            if (!CollectionUtils.isEmpty(areaIds)) {
                params.put("areaFlag", 1);
                if (!CollectionUtils.isEmpty(brandIds)) {
                    params.put("brandFlag", 1);
                }
            } else if (!CollectionUtils.isEmpty(brandIds)) {
                params.put("brandFlag", 1);
            }
        }

        if (UserRoleUtils.isBranch(list)) {//网点
            Integer dotId = getDotId(userId);
            params.put("dotId", dotId != null ? dotId : -1);
            params.put("brandFlag", null);
            params.put("areaFlag", null);
        } else if (UserRoleUtils.isService(list)) {//服务兵
            LambdaQueryWrapper<BizAttendantEntity> bizAttendantWrapper = Wrappers.lambdaQuery();
            bizAttendantWrapper.eq(BizAttendantEntity::getUserId, userId);
            List<BizAttendantEntity> bizAttendantEntities = bizAttendantDao.selectList(bizAttendantWrapper);
            params.put("serviceId", bizAttendantEntities.isEmpty() ? "null" : bizAttendantEntities.get(0).getId());
            params.put("brandFlag", null);
            params.put("areaFlag", null);
        }

        IPage<WorderInformationEntity> page = baseMapper.companyReviewFailedQueryAll(new Query<WorderInformationEntity>().getPageNotI(params), params);
        PageUtils pageUtils = handleValue(new PageUtils(page), new ArrayList<>());

        return pageUtils;
    }


    /**
     * 运营看板查看明细工单数据导出
     */
    @Override
    public PageUtils queryWorderOvertime(Map<String, Object> params) {

        cockpitDataHandleCondition(params);

        if (!com.youngking.lenmoncore.common.utils.StringUtils.isEmpty(params.get("company"))) {
            Integer companyType = baseMapper.getCompanyType(String.valueOf(params.get("company")));
            if (companyType == 0) {
                params.put("companyId", params.get("company"));
            } else if (companyType == 1) {
                params.put("fzx", params.get("company"));
            } else if (companyType == 2) {
                params.put("jxs", params.get("company"));
            }
        }


        IPage<WorderInformationEntity> page = baseMapper.queryWorderOvertimeList(new Query<WorderInformationEntity>().getPageNotI(params), params);
        PageUtils pageUtils = handleValue(new PageUtils(page), new ArrayList<>());
        return pageUtils;
    }

    /**
     * 运营看板查看明细工单数据导出
     */
    @Override
    public List<ExportWorderOvertimeVo> exportWorderOvertime(Map<String, Object> params) {

        cockpitDataHandleCondition(params);

        if (!com.youngking.lenmoncore.common.utils.StringUtils.isEmpty(params.get("company"))) {
            Integer companyType = baseMapper.getCompanyType(String.valueOf(params.get("company")));
            if (companyType == 0) {
                params.put("companyId", params.get("company"));
            } else if (companyType == 1) {
                params.put("fzx", params.get("company"));
            } else if (companyType == 2) {
                params.put("jxs", params.get("company"));
            }
        }


        List<ExportWorderOvertimeVo> list = baseMapper.exportWorderOvertimeList(params);
        return list;
    }


    /**
     * 查询所有合作网点工单数据
     */
    @Override
    public PageUtils queryThird(Map<String, Object> params) {
        if (!com.youngking.lenmoncore.common.utils.StringUtils.isEmpty(params.get("company"))) {
            Integer companyType = baseMapper.getCompanyType(String.valueOf(params.get("company")));
            if (companyType == 0) {
                params.put("companyId", params.get("company"));
            } else if (companyType == 1) {
                params.put("fzx", params.get("company"));
            } else if (companyType == 2) {
                params.put("jxs", params.get("company"));
            }
        }
        if (!com.youngking.lenmoncore.common.utils.StringUtils.isEmpty(params.get("regionLabelList"))) {
            String regionLabelList = Objects.toString(params.get("regionLabelList"));
            String[] areas = regionLabelList.split(",");
            List<String> areaId = new ArrayList<>();
            List<String> area = new ArrayList<>();
            for (int i = 0; i < areas.length; i++) {
                QueryWrapper<BizRegionEntity> query = new QueryWrapper<>();
                String id = areas[i];
                query.eq("id", id);
                BizRegionEntity bizRegionEntity = bizRegionDao.selectOne(query);
                if (bizRegionEntity.getType() == 1) {
                    //省
                    area.add(bizRegionEntity.getId().toString());
                } else if (bizRegionEntity.getType() == 2) {
                    //市
                    QueryWrapper<BizRegionEntity> queryWrapper = new QueryWrapper<>();
                    queryWrapper.eq("pid", bizRegionEntity.getId());
                    List<BizRegionEntity> bizRegionEntities = bizRegionDao.selectList(queryWrapper);
                    for (int j = 0; j < bizRegionEntities.size(); j++) {
                        areaId.add(bizRegionEntities.get(j).getId().toString());
                    }
                } else if (bizRegionEntity.getType() == 3) {
                    //县
                    areaId.add(bizRegionEntity.getId().toString());
                }
            }

            if (area != null && area.size() > 0) {
                params.put("areas", area);
            }
            if (areaId != null && areaId.size() > 0) {
                params.put("areaIds", areaId);
            }
        }
        SysUserEntity user = getUser();
        List<ManagerAreaBrandDto> userAreaBrand = baseMapper.getUserAreaBrand(user.getUserId());
        if (!CollectionUtils.isEmpty(userAreaBrand)) {
            List<Integer> areaIds = userAreaBrand.stream().filter(o -> o.getAreaId() != null).map(ManagerAreaBrandDto::getAreaId).collect(Collectors.toList());
            List<Integer> brandIds = userAreaBrand.stream().filter(o -> o.getBrandId() != null).map(ManagerAreaBrandDto::getBrandId).collect(Collectors.toList());
            params.put("userId", user.getUserId());
            if (!CollectionUtils.isEmpty(areaIds)) {
                params.put("areaFlag", 1);
                if (!CollectionUtils.isEmpty(brandIds)) {
                    params.put("brandFlag", 1);
                }
            } else if (!CollectionUtils.isEmpty(brandIds)) {
                params.put("brandFlag", 1);
            }
        }
        IPage<WorderInformationEntity> page = baseMapper.getOrderThirdList(new Query<WorderInformationEntity>().getPageNotI(params), params);
        PageUtils pageUtils = handleValue(new PageUtils(page), new ArrayList<>());
        return pageUtils;
    }


    /**
     * 根据woderId获取工单信息
     *
     * @param worderId
     * @return
     */
    @Override
    public WorderInfoEntity getByWorderId(Integer worderId) {
        WorderInfoEntity worderInformation = baseMapper.getByWorderId(worderId);
        worderInformation.setCreatorName(baseMapper.getUserName(worderInformation.getCreator()));
        worderInformation.setCreateByName(baseMapper.getUserName(worderInformation.getCreateBy()));
        String worderStatusName = baseMapper.getStatusName(worderInformation.getWorderStatus());
        worderInformation.setWorderStatusValue(worderStatusName);
        WorderInfoEntity worderInfoEntity = handleInfo(worderInformation);
        Integer ticketStatus = worderInformation.getTicketStatus();
        worderInfoEntity.setTicketStatus(ticketStatus);
        if (worderInformation.getUserActualCost() != null && worderInformation.getUserActualCost().compareTo(BigDecimal.ZERO) >= 0) {
            worderInfoEntity.setIsPay("已支付");
        } else {
            worderInfoEntity.setIsPay("未支付");
        }
        QueryWrapper<WorderOperationRecodeEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("worder_no", worderInformation.getWorderNo());
        wrapper.eq("worder_status", worderInformation.getWorderStatus());
        wrapper.eq("worder_exec_status", worderInformation.getWorderExecStatus());
        wrapper.orderByAsc("create_time");
        wrapper.last("limit 1");
        WorderOperationRecodeEntity recodeEntity = worderOperationRecodeDao.selectOne(wrapper);
        if (recodeEntity != null) {
            worderInfoEntity.setRecord(recodeEntity.getRecord());
        }
        return worderInfoEntity;
    }

    @Override
    public WorderInformationEntity getByWorderNo(String worderNo) {
        return this.lambdaQuery()
                .eq(WorderInformationEntity::getWorderNo, worderNo)
                .one();
    }

    @Override
    public WorderInformationEntity getByCompanyWorderNo(String companyWorderNo) {
        return this.lambdaQuery()
                .eq(WorderInformationEntity::getCompanyOrderNumber, companyWorderNo)
                .one();
    }

    @Override
    public Boolean checkExists(String worderNo) {
        Integer count = this.lambdaQuery()
                .eq(WorderInformationEntity::getWorderNo, worderNo)
                .count();
        return count > 0;
    }

    public WorderInfoEntity handleInfo(WorderInfoEntity info) {
        Integer worderExecStatus = info.getWorderExecStatus(); //工单执行状态
        if (worderExecStatus != null) {
            List<ExtFieldDictionaryDto> worderExecStatusList = extFieldDao.findByDicNumber("worder_exec_status");
            worderExecStatusList.forEach(execStatus -> {
                if (worderExecStatus.toString().equals(execStatus.getDetailNumber())) {
                    info.setWorderExecStatusValue(execStatus.getDetailName());
                }
            });
        }
        return info;
    }

    /**
     * 管理员查询
     */
    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        String worderStatus = (String) params.get("worderStatus");
        if (StringUtils.isNotBlank(worderStatus) && "4".equals(worderStatus)) {
            params.put("worderExecStatuses", "(16 , 17, 20, 21, 22)");
            //params.put("worderExecStatus", null);
            params.put("worderStatus", null);
        }
        IPage<WorderInfoEntity> page = baseMapper.getWorderInformation(new Query<WorderInfoEntity>().getPage(params), params);
        worderSceneService.getScenePermission(page.getRecords());
        return new PageUtils(page);
    }

    /**
     * 其他角色区域加品牌权限查询
     */
    public PageUtils queryAreaBrandPage(Map<String, Object> params) {
        String worderStatus = (String) params.get("worderStatus");
        if (StringUtils.isNotBlank(worderStatus) && "4".equals(worderStatus)) {
            params.put("worderExecStatuses", "(17, 20, 21)");
            //params.put("worderExecStatus", null);
            params.put("worderStatus", null);
        }
        //查询当前用户下所有关联的
        IPage<WorderInfoEntity> page = baseMapper.getWorderInformation(new Query<WorderInfoEntity>().getPage(params), params);
        worderSceneService.getScenePermission(page.getRecords());
        return new PageUtils(page);
    }

    /**
     * 客服查询
     */
    public PageUtils queryPageCustomer(Map<String, Object> params) {
        String worderStatus = (String) params.get("worderStatus");
        if (StringUtils.isNotBlank(worderStatus) && "4".equals(worderStatus)) {
            params.put("worderExecStatuses", "(16 , 17, 20, 21, 22)");
            //params.put("worderExecStatus", null);
            params.put("worderStatus", null);
        }

        IPage<WorderInfoEntity> page = baseMapper.getListCustomer(new Query<WorderInfoEntity>().getPageNotI(params), params);
        worderSceneService.getScenePermission(page.getRecords());
        return new PageUtils(page);
    }

    /**
     * 项目经理查询
     */
    @Override
    public PageUtils queryPagePM(Map<String, Object> params) {
        String worderStatus = (String) params.get("worderStatus");
        if (StringUtils.isNotBlank(worderStatus) && "4".equals(worderStatus)) {
            params.put("worderExecStatuses", "(16 , 17, 20, 21, 22)");
            //params.put("worderExecStatus", null);
            params.put("worderStatus", null);
        }
        IPage<WorderInfoEntity> page = baseMapper.getListPM(new Query<WorderInfoEntity>().getPageNotI(params), params);
        worderSceneService.getScenePermission(page.getRecords());
        return new PageUtils(page);
    }

    /**
     * 服务兵查询
     */
    @Override
    public PageUtils queryService(Map<String, Object> params) {
        String worderStatus = (String) params.get("worderStatus");
        if (StringUtils.isNotBlank(worderStatus) && "4".equals(worderStatus)) {
            params.put("worderExecStatuses", "(16 , 17, 20, 21, 22)");
            //params.put("worderExecStatus", null);
            params.put("worderStatus", null);
        }
        IPage<WorderInfoEntity> page = baseMapper.getListService(new Query<WorderInfoEntity>().getPageNotI(params), params);
        worderSceneService.getScenePermission(page.getRecords());
        return new PageUtils(page);
    }

    /**
     * 网点查询
     */
    @Override
    public PageUtils queryPageBranch(Map<String, Object> params) {
        String worderStatus = (String) params.get("worderStatus");
        if (StringUtils.isNotBlank(worderStatus) && "4".equals(worderStatus)) {
            params.put("worderExecStatuses", "(16 , 17, 20, 21, 22)");
            //params.put("worderExecStatus", null);
            params.put("worderStatus", null);
        }
        IPage<WorderInfoEntity> page = baseMapper.getListBranch(new Query<WorderInfoEntity>().getPageNotI(params), params);
        worderSceneService.getScenePermission(page.getRecords());
        return new PageUtils(page);
    }


    /**
     * 查询物料信息
     *
     * @param worderId
     * @return
     */
    @Override
    public List<WorderMaterielVo> getMateriel(Integer worderId) {
        return baseMapper.getListMateriel(worderId);
    }

    /**
     * 查询操作记录
     *
     * @param worderNo
     * @return
     */
    @Override
    public List<String> queryOperationRecord(String worderNo) {
        return baseMapper.getOperationRecord(worderNo);
    }

    /**
     * 查询第三方操作记录
     *
     * @param worderNo
     * @return
     */
    @Override
    public List<String> queryThirdOperationRecord(String worderNo) {
        return baseMapper.getThirdOperationRecord(worderNo);
    }

    /**
     * 查询工单备注
     *
     * @param worderNo
     * @return
     */
    @Override
    public List<WorderRemarkLogEntity> queryRemarkLog(String worderNo) {
        return worderRemarkLogService.list(new QueryWrapper<WorderRemarkLogEntity>().eq("worder_no", worderNo).orderByDesc("create_time"));
    }

    /**
     * 查询工单结算费用明细
     *
     * @param worderId
     */
    @Override
    public List<FeeDetailDto> getFeeDetail(Integer worderId) {
        List<FeeDetailDto> feeDetailDtoList = baseMapper.getFeeDetail(worderId);
        return feeDetailDtoList;
    }

    /**
     * 查询用户增项费用
     *
     * @param worderId
     */
    @Override
    public IncreaseFeeDto getIncreaseFee(Integer worderId) {
        IncreaseFeeDto increaseFeeDto = new IncreaseFeeDto();
        try {

            WorderUserBalanceFeeVO worderUserBalanceFeeVO = branchBalanceService.caculateOneUserBalanceFee(worderId);
            if (!CollectionUtils.isEmpty(worderUserBalanceFeeVO.getDetails())) {
                System.out.println("*****************************************1");
                increaseFeeDto.setWorderId(worderId);
                increaseFeeDto.setUserBalanceFee(worderUserBalanceFeeVO.getUserBalanceFee());
                increaseFeeDto.setUserActualCost(worderUserBalanceFeeVO.getUserActualCost());
                increaseFeeDto.setMinFee(worderUserBalanceFeeVO.getMinFee());
                List<Map<String, Object>> materielList = worderUserBalanceFeeVO.getDetails().stream().map(o -> {
                    Map<String, Object> materiel = baseMapper.getMaterielById(o.getMaterielId());
                    materiel.put("num", o.getNum());
                    materiel.put("balanceFee", o.getBalanceFee());
                    return materiel;
                }).collect(Collectors.toList());
                increaseFeeDto.setDetails(materielList);
            } else {
                System.out.println("*****************************************2");
                increaseFeeDto.setWorderId(worderId);
                increaseFeeDto.setUserBalanceFee(new BigDecimal(0));
                increaseFeeDto.setMinFee(new BigDecimal(0));
                increaseFeeDto.setDetails(new ArrayList<>());
                increaseFeeDto.setUserActualCost(BigDecimal.ZERO);
            }
        } catch (Exception e) {
            System.out.println("*****************************************3");
            log.error("getIncreaseFee error:", e);
            increaseFeeDto.setWorderId(worderId);
            increaseFeeDto.setUserBalanceFee(new BigDecimal(0));
            increaseFeeDto.setMinFee(new BigDecimal(0));
            increaseFeeDto.setDetails(new ArrayList<>());
            increaseFeeDto.setUserActualCost(BigDecimal.ZERO);
        }
        return increaseFeeDto;
    }


    /**
     * 工单类型下拉框数据
     *
     * @return
     */
    @Override
    public List<WorderTypeDto> listWorderType() {
        List<WorderTypeDto> worderTypeDtos = baseMapper.listWorderType();
        return worderTypeDtos;
    }

    /**
     * 修改工单状态
     *
     * @param worderId
     * @param flag
     * @return
     */
    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public String updateStatus(Integer worderId, Integer flag) {
        Map<String, Object> map = new HashMap<>();
        map.put("worderId", worderId);
        String worderStatus = null;
        String worderExecStatus = null;
        String result = null;
        WorderInfoEntity worderInfo = baseMapper.getByWorderId(worderId);
        if (flag == 1) {
            //车企确认勘测完成，工单状态改为9，等待充电桩
            map.put("worderExecStatus", 9);
            map.put("worderExecStatusValue", "等待充电桩");
            map.put("worderStatus", 2); //主状态改为2，安装中
            map.put("worderStatusValue", "安装中");
            worderStatus = Constant.INSTALL;
            worderExecStatus = Constant.WAIT_CHARGING_PILE;
            result = "等待充电桩";
            if (flowCommon.hasFlowByWorderNo(worderInfo.getWorderNo())) {
                //调用等待充电桩流程
                ExecuteFlowResultPo executeFlowResultPo = flowCommon.executeFlow(worderInfo.getWorderNo(), FlowConstant.ProcessCode.CompConfSurv, FlowConstant.ProcessStatus.Y);
                // 流程调用失败直接返回
                if (!"0".equals(executeFlowResultPo.getCode())) {
                    return executeFlowResultPo.getMsg();
                }
            } else {
                baseMapper.updateWorderExecStatus(map);
                baseMapper.updateWorderStatus(map);
            }
            worderTypeService.checkWorderTypeByWorderId(worderId, WarningConstant.COMPANY_FINISH_CONVEY);
        } else if (flag == 2) {
            MapUtils params = new MapUtils().put("worder_id", worderId);
            List<WorderInformationEntity> worderInformationEntities = worderInformationDao.selectByMap(params);
            Integer worderTypeId = null;
            if (worderInformationEntities.size() > IntegerEnum.ZERO.getValue() && null != worderInformationEntities.get(IntegerEnum.ZERO.getValue())) {
                WorderInformationEntity worderInformationEntity = worderInformationEntities.get(IntegerEnum.ZERO.getValue());
                worderTypeId = worderInformationEntity.getWorderTypeId();

            }
            //勘测
            if (null != worderTypeId && IntegerEnum.FOUR.getValue().equals(worderTypeId)) {
                //车企确认安装完成，工单状态改为17，安装完成
                map.put("worderExecStatus", 17);
                map.put("worderExecStatusValue", "安装完成");
                map.put("worderStatus", 3); //主状态改为3，结算中
                map.put("worderStatusValue", "结算中");
                map.put("worderSetStatus", 0);   //工单结算状态0，工单待计算
                map.put("worderSetStatusValue", "工单待计算");
                result = "安装完成";
                worderStatus = Constant.INSTALL;
                worderExecStatus = Constant.INSTALL_END;
                if (flowCommon.hasFlowByWorderNo(worderInfo.getWorderNo())) {
                    //调用安装完成
                    ExecuteFlowResultPo executeFlowResultPo = flowCommon.executeFlow(worderInfo.getWorderNo(), FlowConstant.ProcessCode.CompConfFix, FlowConstant.ProcessStatus.Y);
                    if (!"0".equals(executeFlowResultPo.getCode())) {
                        return executeFlowResultPo.getMsg();
                    }
                    baseMapper.updateWorderFinishTime(map);
                } else {
                    baseMapper.updateWorderExecStatus(map);
                    baseMapper.updateWorderStatus(map);
                    baseMapper.updateWorderSetStatus(map);
                    baseMapper.updateWorderFinishTime(map);
                }
                // 加入待工单结算-车企待结算
                worderWaitAccountService.addWorderWaitAccount(worderId);
            } else {
                // 增项费用大于0，实际未收费
                if (null != worderInfo.getUserBalanceFee() && worderInfo.getUserBalanceFee().compareTo(BigDecimal.ZERO) == 1) {
                    if (worderInfo.getUserActualCost() == null || worderInfo.getUserActualCost().compareTo(BigDecimal.ZERO) == 0) {
                        return result = "用户增项费用未收，不能结单";
                    }
                }
                // 实际已经收费，未开票
                if (null != worderInfo.getUserActualCost() && worderInfo.getUserActualCost().compareTo(BigDecimal.ZERO) == 1) {
//					Integer invoiceStatus = baseMapper.getInvoiceStatus(worderId);
                    List<WorderOrderLogDTO> worderOrderLogs = worderOrderLogMapper.findOrderLogInfoList(worderInfo.getWorderNo(), 2);
                    if (worderOrderLogs != null && worderOrderLogs.size() > 0) {
                        WorderOrderLogDTO worderOrderLog = worderOrderLogs.get(0);
                        if (!worderOrderLog.isApply()) {
                            return result = "用户未申请开票";
                        }
                    }
                    /*if (invoiceStatus == null || invoiceStatus != 3) {
						return result = "用户增项费用已收，未开发票，不能结单";
					}*/
                }
                //车企确认安装完成，工单状态改为17，安装完成
                map.put("worderExecStatus", 17);
                map.put("worderExecStatusValue", "安装完成");
                map.put("worderStatus", 3); //主状态改为3，结算中
                map.put("worderStatusValue", "结算中");
                map.put("worderSetStatus", 0);   //工单结算状态0，工单待计算
                map.put("worderSetStatusValue", "工单待计算");
                result = "安装完成";
                worderStatus = Constant.INSTALL;
                worderExecStatus = Constant.INSTALL_END;
                if (flowCommon.hasFlowByWorderId(worderId)) {
                    //调用安装完成
                    ExecuteFlowResultPo executeFlowResultPo = flowCommon.executeFlow(worderInfo.getWorderNo(), FlowConstant.ProcessCode.CompConfFix, FlowConstant.ProcessStatus.Y);
                    if (!"0".equals(executeFlowResultPo.getCode())) {
                        return executeFlowResultPo.getMsg();
                    }
                    baseMapper.updateWorderFinishTime(map);
                } else {
                    baseMapper.updateWorderExecStatus(map);
                    baseMapper.updateWorderStatus(map);
                    baseMapper.updateWorderSetStatus(map);
                    baseMapper.updateWorderFinishTime(map);
                }
                // 加入待工单结算-车企待结算
                worderWaitAccountService.addWorderWaitAccount(worderId);
            }
//			if(worderInfo.getUserBalanceFee() != null ){
//				if( worderInfo.getUserBalanceFee().compareTo(BigDecimal.ZERO) == 1) {
//					if (worderInfo.getUserActualCost() != null && worderInfo.getUserActualCost().compareTo(BigDecimal.ZERO) == 1) {
//						Integer invoiceStatus = baseMapper.getInvoiceStatus(worderId);
//						if (invoiceStatus != null && invoiceStatus == 3) {
//							//车企确认安装完成，工单状态改为17，安装完成
//							map.put("worderExecStatus", 17);
//							map.put("worderExecStatusValue", "安装完成");
//							map.put("worderStatus", 3); //主状态改为3，结算中
//							map.put("worderStatusValue", "结算中");
//							map.put("worderSetStatus", 0);   //工单结算状态0，工单待计算
//							map.put("worderSetStatusValue", "工单待计算");
//							result = "安装完成";
//							worderStatus = Constant.INSTALL;
//							worderExecStatus = Constant.INSTALL_END;
//							baseMapper.updateWorderExecStatus(map);
//							baseMapper.updateWorderStatus(map);
//							baseMapper.updateWorderSetStatus(map);
//							baseMapper.updateWorderFinishTime(map);
//						} else {
//							return result = "用户增项费用已收，未开发票，不能结单";
//						}
//					} else {
//						return result = "用户增项费用未收，不能结单";
//					}
//				} else {
//					//车企确认安装完成，工单状态改为17，安装完成
//					map.put("worderExecStatus", 17);
//					map.put("worderExecStatusValue", "安装完成");
//					map.put("worderStatus", 3); //主状态改为3，结算中
//					map.put("worderStatusValue", "结算中");
//					map.put("worderSetStatus", 0);   //工单结算状态0，工单待计算
//					map.put("worderSetStatusValue", "工单待计算");
//					result = "安装完成";
//					worderStatus = Constant.INSTALL;
//					worderExecStatus = Constant.INSTALL_END;
//					baseMapper.updateWorderExecStatus(map);
//					baseMapper.updateWorderStatus(map);
//					baseMapper.updateWorderSetStatus(map);
//					baseMapper.updateWorderFinishTime(map);
//					//待定
//					//return result = "用户增项费用为0";
//				}
//			}else {
//				return result = "用户增项费用未收，不能结单";
//			}
        } else if (flag == 3) {
            //车企退回，工单状态改为14，安装资料整改中
            map.put("worderExecStatus", 14);
            map.put("worderExecStatusValue", "安装资料整改中");
            result = "安装资料整改中";
            worderStatus = Constant.INSTALL;
            worderExecStatus = Constant.INSTALL_REFORM;
            if (flowCommon.hasFlowByWorderId(worderId)) {
                ExecuteFlowResultPo executeFlowResultPo = flowCommon.executeFlow(worderInfo.getWorderNo(), FlowConstant.ProcessCode.FixDoc2Audit, FlowConstant.ProcessStatus.N);
                if (!"0".equals(executeFlowResultPo.getCode())) {
                    return executeFlowResultPo.getMsg();
                }
            } else {
                baseMapper.updateWorderExecStatus(map);
            }
        } else if (flag == 4) {
            //维护工单，工单状态改为10，安装待预约
            map.put("worderExecStatus", 10);
            map.put("worderExecStatusValue", "待安装预约");
            map.put("worderStatus", 2); //主状态改为2，安装中
            map.put("worderStatusValue", "安装中");
            worderStatus = Constant.INSTALL;
            worderExecStatus = Constant.INSTALL_NOT_APPOINT;
            result = "待安装预约";
            if (flowCommon.hasFlowByWorderId(worderId)) {
                ExecuteFlowResultPo executeFlowResultPo = flowCommon.executeFlow(worderInfo.getWorderNo(), FlowConstant.ProcessCode.BookingFix, FlowConstant.ProcessStatus.Y);
                if (!"0".equals(executeFlowResultPo.getCode())) {
                    return executeFlowResultPo.getMsg();
                }
            } else {
                baseMapper.updateWorderExecStatus(map);
                baseMapper.updateWorderStatus(map);
            }
        }
        //WorderInformationEntity worderInfo = baseMapper.selectById(worderId);
        //WorderInfoEntity worderInfo = baseMapper.getByWorderId(worderId);
        SysUserEntity sysUser = (SysUserEntity) SecurityUtils.getSubject().getPrincipal();
        OperationRecord operationRecord = new OperationRecord(sysUser.getUserId(), sysUser.getUsername(), result, worderInfo.getWorderNo());
        operationRecord.setWorderStatus(worderStatus);
        operationRecord.setWorderExecStatus(worderExecStatus);
        workMsgDao.insertOperation(operationRecord);
        return result;
    }

    /**
     * 取消服务
     *
     * @param worderRemarkLog
     */
    @Override
    public void cancelService(WorderRemarkLogEntity worderRemarkLog) {

    }

    @Override
    public void cancelWorder(String worderNo, String companyOrderNumber) {
        this.lambdaUpdate()
                .eq(StringUtils.isNotBlank(worderNo), WorderInformationEntity::getWorderNo, worderNo)
                .eq(StringUtils.isNotBlank(companyOrderNumber), WorderInformationEntity::getCompanyOrderNumber, companyOrderNumber)
                .set(WorderInformationEntity::getWorderStatus, 6)
                .set(WorderInformationEntity::getWorderExecStatus, 21)
                .update();
    }

    private String getNum(Integer integer) {
        if (integer > 999999) {
            throw new RuntimeException();
        }
        return String.format("%06d", integer); //25为int型
    }

    /**
     * 查询网点id
     *
     * @param userId
     * @return
     */
    public Integer getDotId(Long userId) {
        return baseMapper.getDotId(userId);
    }

    /**
     * 获取用户
     *
     * @return
     */
    public SysUserEntity getUser() {
        return (SysUserEntity) SecurityUtils.getSubject().getPrincipal();
    }

    @Override
    public R queryCollecDetailByWorderId(Integer worderId) {
        CollecDetailPo collecDetailPo = baseMapper.queryCollecDetailByWorderId(worderId);
        if (collecDetailPo == null) {
            return R.error("未查询到记录");
        }
        return R.ok().put("CollecDetail", collecDetailPo);
    }

    /**
     * 预约服务，预约勘测，预约安装等
     *
     * @param appointServiceVo
     */
    @Override
    @Transactional
    public R updateAppointTime(AppointServiceVo appointServiceVo) {
        String worderNo = appointServiceVo.getWorderNo();
        if (appointServiceVo.getFlag() == 1) {
            String time = appointServiceVo.getConveyAppointTime();
            if (StringUtils.isNotEmpty(time)) {
                time = time.substring(0, 13) + ":00:00";
                appointServiceVo.setConveyAppointTime(time);
            }

        }
        if (appointServiceVo.getFlag() == 2) {
            String time = appointServiceVo.getInstallAppointTime();
            if (StringUtils.isNotEmpty(time)) {
                time = time.substring(0, 13) + ":00:00";
                appointServiceVo.setInstallAppointTime(time);
            }

        }

        Map<String, Object> map = new HashMap<>();
        map.put("worderNo", worderNo);
        SysUserEntity user = getUser();
        //添加操作记录
        OperationRecord operationRecord = new OperationRecord();
        operationRecord.setUserId(user.getUserId());
        operationRecord.setOperationUser(user.getUsername());
        operationRecord.setWorderNo(worderNo);
        //获取用户角色信息
        List<SysRoleEntity> list = sysRoleService.getByUserId(user.getUserId());
        //添加每个客服只能看到自己创建的工单
        if (UserRoleUtils.isCustomerService(list)) {//客服
            operationRecord.setType(1);
        } else if (UserRoleUtils.isPM(list)) {//项目经理
            operationRecord.setType(2);
        } else if (UserRoleUtils.isBranch(list)) {//网点
            operationRecord.setType(3);
        } else {
            operationRecord.setType(1);
        }

        String worderStatus = null;
        String worderExecStatus = null;
        String worderTriggerEvent = null;
        //修改工单执行状态，预约时间 勘测
        if (appointServiceVo.getFlag() == 1) {
            QueryWrapper<WorderInformationEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("worder_no", worderNo);
            WorderInformationEntity worderInformationEntity = worderInformationDao.selectOne(queryWrapper);
            if (worderInformationEntity.getWorderStatus() == 1 && StringUtils.isNotEmpty(worderInformationEntity.getServiceId().toString())) {
                //添加备注
                if (StringUtils.isNotEmpty(appointServiceVo.getContent())) {
                    WorderRemarkLogEntity worderRemarkLog = new WorderRemarkLogEntity();
                    worderRemarkLog.setWorderNo(worderNo);   //工单编号
                    if (StringUtils.isNotBlank(worderRemarkLog.getNoInstallReason()) && worderRemarkLog.getNoInstallReason().equals("-1")) {
                        return R.error("请选择原因");
                    }
                    if (StringUtils.isNotEmpty(appointServiceVo.getNoInstallReason())) {
                        SysDictionaryDetailEntity sysDictionaryDetailEntity = sysDictionaryDetailService.getById(appointServiceVo.getNoInstallReason());
                        worderRemarkLog.setNoInstallReasonId(sysDictionaryDetailEntity.getId());
                        worderRemarkLog.setContent(sysDictionaryDetailEntity.getDetailName() + "_" + appointServiceVo.getContent() + " " + appointServiceVo.getConveyAppointTime());  //备注内容
                    } else {
                        worderRemarkLog.setContent(appointServiceVo.getContent() + " " + appointServiceVo.getConveyAppointTime());  //备注内容
                    }
                    worderRemarkLog.setCreateTime(new Date());
                    SimpleDateFormat formatDate = new SimpleDateFormat("yyyy-MM-dd");
                    String title = formatDate.format(new Date()) + " " + user.getUsername() + "预约勘测";
                    worderRemarkLog.setTitle(title);
                    worderRemarkLogService.save(worderRemarkLog);
                }
                // 通知比亚迪系统
                Result providerBusinessResult = providerBusinessService.callBusiness(BusinessProcessPo.builder().worderNo(appointServiceVo.getWorderNo()).conveyAppointTime(appointServiceVo.getConveyAppointTime()).build(), "pushContactInfo");
                if (providerBusinessResult.getCode() != 0) {
                    return R.error(providerBusinessResult.getCode(), providerBusinessResult.getMsg());
                }
                //通知长安系统
                try {
                    List<WorderRemarkLogEntity> worderRemarkLogEntityList = worderRemarkLogService.queryConnectTime(worderInformationEntity.getWorderNo());
                    Date firstContactTime = null;
                    if (worderRemarkLogEntityList != null && worderRemarkLogEntityList.size() > 0) {
                        firstContactTime = worderRemarkLogEntityList.get(0).getCreateTime();
                    } else {
                        firstContactTime = new Date();
                    }
                    WorderInformationAttributeEntity worderInformationAttributeEntity =
                            worderInformationAttributeDao.selectAttributeByWorderNo(worderNo, "push_updateFirstcontactTime", "ca");
                    if (worderInformationAttributeEntity != null && worderInformationAttributeEntity.getAttributeValue().equals("0")) {
                        CaApiResponse response = caApiService.pushFirstcontactTime(worderNo, worderInformationEntity.getCompanyOrderNumber(), firstContactTime);
                        if (!response.getSuccess()) {
                            return R.error(Integer.parseInt(response.getCode()), response.getMessage());
                        }
                        worderInformationAttributeEntity.setAttributeValue("1");
                        worderInformationAttributeDao.updateById(worderInformationAttributeEntity);
                    }
                    CaApiResponse response2 = caApiService.pushMeasureTime(worderNo, worderInformationEntity.getCompanyOrderNumber(), appointServiceVo.getConveyAppointTime());
                    if (!response2.getSuccess()) {
                        return R.error(Integer.parseInt(response2.getCode()), response2.getMessage());
                    }
                } catch (IOException e) {
                    return R.error(500, "调用长安接口异常");
                }

                gacePushService.pushOrder(worderInformationEntity.getWorderNo(), WorkOrderStatusEnum.APPROVED, "已预约勘测");


                String record = user.getUsername() + "预约勘测";
                operationRecord.setRecord(record);
                worderStatus = Constant.CONVEY;
                worderExecStatus = Constant.NOT_CONVEY;
                worderTriggerEvent = WarningConstant.CONVEY_APPOINT;
                //勘测预约


                //如果是3（待勘测预约）的话 改变流程
                Boolean updateFlow = worderInformationEntity.getWorderExecStatus() == 2;
                //如果是待勘测预约

                if (updateFlow) {
                    //工单执行状态改为3 ，待勘测
                    map.put("worderStatus", 1);
                    map.put("worderExecStatus", 3);
                }
                map.put("conveyAppointTime", appointServiceVo.getConveyAppointTime());   //修改勘测预约时间
                map.put("worderLevel", appointServiceVo.getWorderLevel());
                //修改预约时间
                if (updateFlow && flowCommon.hasFlowByWorderNo(worderNo)) {
                    //调用预约勘测流程
                    ExecuteFlowResultPo executeFlowResultPo = flowCommon.executeFlow(worderNo, FlowConstant.ProcessCode.BookingSurv, FlowConstant.ProcessStatus.Y);
                    // 流程调用失败直接返回
                    if (!"0".equals(executeFlowResultPo.getCode())) {
                        return R.error(executeFlowResultPo.getMsg());
                    }
                    baseMapper.updateTimeAppoint(map);
                } else {
                    baseMapper.updateAppointTime(map);
                }

                worderBuffService.saveConveyFieldsInAheadWithDefaultValue(worderNo);
            } else {
                return R.error("工单当前不在您名下，无法执行勘测预约");
            }
        } else if (appointServiceVo.getFlag() == 2) {
            //添加备注
            SysDictionaryDetailEntity sysDictionaryDetailEntity = sysDictionaryDetailService.getOne(new QueryWrapper<SysDictionaryDetailEntity>().eq("id", appointServiceVo.getNoInstallReason()));
            if (StringUtils.isNotEmpty(appointServiceVo.getContent())) {
                WorderRemarkLogEntity worderRemarkLog = new WorderRemarkLogEntity();
                worderRemarkLog.setWorderNo(worderNo);   //工单编号
                if (StringUtils.isNotBlank(worderRemarkLog.getNoInstallReason()) && worderRemarkLog.getNoInstallReason().equals("-1")) {
                    return R.error("请选择原因");
                }
                if (StringUtils.isNotEmpty(appointServiceVo.getNoInstallReason())) {

                    worderRemarkLog.setNoInstallReasonId(sysDictionaryDetailEntity.getId());
                    worderRemarkLog.setContent(sysDictionaryDetailEntity.getDetailName() + "_" + appointServiceVo.getContent() + " " + appointServiceVo.getInstallAppointTime());  //备注内容
                } else {
                    worderRemarkLog.setContent(appointServiceVo.getContent() + " " + appointServiceVo.getInstallAppointTime());  //备注内容
                }
                worderRemarkLog.setCreateTime(new Date());
                SimpleDateFormat formatDate = new SimpleDateFormat("yyyy-MM-dd");
                String title = formatDate.format(new Date()) + " " + user.getUsername() + "预约安装";
                worderRemarkLog.setTitle(title);
                worderRemarkLogService.save(worderRemarkLog);
            }

            String processCode = "pushContactInfo";
            WorderInformationEntity worderInformationEntity = worderInformationDao.selectOne(new QueryWrapper<WorderInformationEntity>().eq("worder_no", worderNo));
            if (worderInformationEntity.getWorderTypeId() == 6) {
                processCode = "aoPushContactInfo";
            //检查工单
            } else if (worderInformationEntity.getWorderTypeId() == 8) {
                processCode = "pushCheckContactInfo";
            }
            // 通知比亚迪系统
            Result providerBusinessResult = providerBusinessService.callBusiness(BusinessProcessPo.builder().worderNo(worderNo).installAppointTime(appointServiceVo.getInstallAppointTime()).build(), processCode);
            if (providerBusinessResult.getCode() != 0) {
                return R.error(providerBusinessResult.getCode(), providerBusinessResult.getMsg());
            }
            //调用长安接口
            try {
                String visitTime = LocalDateTime.now().plusMinutes(1).format(DateTimeFormatter.ofPattern(DateUtils.DATE_TIME_PATTERN));
                CaApiResponse response2 = caApiService.pushVisitTime(worderNo, worderInformationEntity.getCompanyOrderNumber(), visitTime);
                if (!response2.getSuccess()) {
                    return R.error(Integer.parseInt(response2.getCode()), response2.getMessage());
                }
            } catch (IOException e) {
                return R.error(500, "调用长安接口异常");
            }

            //调用长城接口
            try {
                SubmitInstallOrderRequest request = new SubmitInstallOrderRequest();
                request.setOrderNo(worderInformationEntity.getCompanyOrderNumber());
                request.setSaveOrSubmit("01");
                request.setHasPowerInstall(
                        (StringUtils.isBlank(sysDictionaryDetailEntity.getDetailName()) ||
                                sysDictionaryDetailEntity.getDetailName().contains("不具备")) ? "N" : "Y");
                if (Objects.equals(request.getHasPowerInstall(), "N")) {
                    request.setNoInstallConditionsRemark(appointServiceVo.getContent());
                }
                changChengWallClient.pushInstallOrderInstall(request, worderInformationEntity.getWorderNo(), worderInformationEntity.getCreateTime());
            } catch (Exception e) {
                log.error("调用长城接口异常", e);
                return R.error(500, "调用长城接口异常");
            }

            //调用广汽工单修改接口，修改工单状态
            gacePushService.pushOrder(worderNo, WorkOrderStatusEnum.APPROVED, "勘测安装中");

            String record = user.getUsername() + "预约安装";
            operationRecord.setRecord(record);
            worderStatus = Constant.INSTALL;
            worderExecStatus = Constant.NOT_INSTALL;
            worderTriggerEvent = WarningConstant.INSTALL_APPOINT;
            //安装预约

            //如果是10（待安装预约）的话 改变流程
            Boolean updateFlow = worderInformationEntity.getWorderExecStatus() == 10;
            //如果是待安装预约
            if (updateFlow) {
                map.put("worderStatus", 2);
                map.put("worderExecStatus", 11);  //工单执行状态改为11，待安装
            }
            map.put("installAppointTime", appointServiceVo.getInstallAppointTime()); //修改安装预约时间
            map.put("worderLevel", appointServiceVo.getWorderLevel());
            //修改预约时间
            if (flowCommon.hasFlowByWorderNo(worderNo) && updateFlow) {
                //调用预约安装流程
                ExecuteFlowResultPo executeFlowResultPo = flowCommon.executeFlow(worderNo, FlowConstant.ProcessCode.BookingFix, FlowConstant.ProcessStatus.Y);
                // 流程调用失败直接返回
                if (!"0".equals(executeFlowResultPo.getCode())) {
                    return R.error(executeFlowResultPo.getMsg());
                }
                baseMapper.updateTimeAppoint(map);
            } else {
                baseMapper.updateAppointTime(map);
            }

            worderBuffService.saveInstallFieldsInAheadWithDefaultValue(worderNo);
        }
        operationRecord.setWorderStatus(worderStatus);
        operationRecord.setWorderExecStatus(worderExecStatus);
        //添加操作记录
        workMsgDao.insertOperation(operationRecord);
        return R.ok().putWorderNo(appointServiceVo.getWorderNo()).putWorderExecStatus(worderExecStatus)
                .putWorderTriggerEvent(worderTriggerEvent);
    }


    /**
     * 首次电联用户时间
     *
     * @param firstCallTimeVo
     */
    @Override
    @Transactional
    public void updateFirstCallTime(FirstCallTimeVo firstCallTimeVo) {

        Map<String, Object> map = new HashMap<>();
        map.put("worderNo", firstCallTimeVo.getWorderNo());
        SysUserEntity user = getUser();

        //添加备注
        if (StringUtils.isNotEmpty(firstCallTimeVo.getContent())) {
            WorderRemarkLogEntity worderRemarkLog = new WorderRemarkLogEntity();
            worderRemarkLog.setWorderNo(firstCallTimeVo.getWorderNo());   //工单编号
            worderRemarkLog.setContent(firstCallTimeVo.getContent());  //备注内容
            worderRemarkLog.setCreateTime(new Date());
            SimpleDateFormat formatDate = new SimpleDateFormat("yyyy-MM-dd");
            String title = formatDate.format(new Date()) + " " + user.getUsername() + "首次电联用户";
            worderRemarkLog.setTitle(title);
            worderRemarkLogService.save(worderRemarkLog);
        }

        //添加或修改电联时间
        if (StringUtils.isNotEmpty(firstCallTimeVo.getFirstCallTime())) {
            map.put("firstCallTime", firstCallTimeVo.getFirstCallTime());
            baseMapper.updateFirstCallTime(map);
        }
        //首次电联用户长城需要推送到车企
        QueryWrapper<WorderInformationEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("worder_no", firstCallTimeVo.getWorderNo());
        WorderInformationEntity worderInformation = getOne(wrapper);
        if (Objects.nonNull(worderInformation)) {
            changChengWallClient.pushInstallOrder(worderInformation, firstCallTimeVo.getFirstCallTime());
        }
    }


    /**
     * 电力报桩
     */
    @Override
    @Transactional
    public void powerPile(PowerPileVo powerPileVo) {

        Map<String, Object> map = new HashMap<>();
        map.put("worderNo", powerPileVo.getWorderNo());
        SysUserEntity user = getUser();

        //添加备注
        if (StringUtils.isNotEmpty(powerPileVo.getContent())) {
            WorderRemarkLogEntity worderRemarkLog = new WorderRemarkLogEntity();
            worderRemarkLog.setWorderNo(powerPileVo.getWorderNo());   //工单编号
            worderRemarkLog.setContent(powerPileVo.getContent());  //备注内容
            worderRemarkLog.setCreateTime(new Date());
            SimpleDateFormat formatDate = new SimpleDateFormat("yyyy-MM-dd");
            String title = formatDate.format(new Date()) + " " + user.getUsername() + "电力报桩";
            worderRemarkLog.setTitle(title);
            worderRemarkLogService.save(worderRemarkLog);
        }

        //添加或修改报桩时间
        map.put("cusApplyPowerTime", powerPileVo.getCusApplyPowerTime());    //客户申请电力报桩时间
        map.put("cusExpectPowerTime", powerPileVo.getCusExpectPowerTime());  //客户电力报桩预计完成时间
        map.put("cusRealPowerTime", powerPileVo.getCusRealPowerTime());      //客户电力报桩实际完成时间

        //添加是否需要电力报桩
        WorderInformationSub savedSub = worderInformationSubService.lambdaQuery().eq(WorderInformationSub::getWorderId, powerPileVo.getWorderId().intValue()).one();
        if (savedSub == null) {
            WorderInformationSub worderInformationSub = new WorderInformationSub();
            worderInformationSub.setWorderId(powerPileVo.getWorderId().intValue());
            worderInformationSub.setElectricityState(powerPileVo.getElectricityState());
            worderInformationSub.setInsertTime(LocalDateTime.now());
            worderInformationSubService.save(worderInformationSub);
        } else {
            WorderInformationSub worderInformationSub = new WorderInformationSub();
            worderInformationSub.setWorderId(savedSub.getId());
            worderInformationSub.setElectricityState(powerPileVo.getElectricityState());
            worderInformationSubService.updateById(worderInformationSub);
        }

        baseMapper.updatePowerPile(map);
    }

    /**
     * 设置网点奖惩
     */
    @Override
    @Transactional
    public void setStimulate(WorderPmStimulateVo worderPmStimulateVo) {
        if (worderPmStimulateVo.getUserId() == null) {
            Long userId = getUser().getUserId();
            worderPmStimulateVo.setUserId(userId);
            worderPmStimulateVo.setUserName(baseMapper.getUserName(userId));
        }

        // 判断是否特殊原因
        if (worderPmStimulateVo.getStimulateReason().equals(-1)) {
            worderPmStimulateVo.setReasonType(IntegerEnum.ONE.getValue());
        } else {
            worderPmStimulateVo.setReasonType(IntegerEnum.ZERO.getValue());
        }

        // 计算网点结算费用
        calculateDotTax(worderPmStimulateVo);
        //计算车企结算费用
        calculateCompanyFee(worderPmStimulateVo);

        baseMapper.addStimulate(worderPmStimulateVo);

        if (worderPmStimulateVo.getFileIds() != null && worderPmStimulateVo.getFileIds().length > 0) {
            for (Integer fileId : worderPmStimulateVo.getFileIds()) {
                worderPmStimulateDao.addStimulateFile(StimulateFileEntity.builder()
                        .stimulateId(worderPmStimulateVo.getId())
                        .fileId(fileId).build());
            }
        }

    }

    /**
     * 修改网点奖惩
     */
    @Override
    @Transactional
    public void updateStimulate(WorderPmStimulateVo worderPmStimulateVo) {
        if (worderPmStimulateVo.getUserId() == null) {
            Long userId = getUser().getUserId();
            worderPmStimulateVo.setUserId(userId);
            worderPmStimulateVo.setUserName(baseMapper.getUserName(userId));
        }

        // 判断是否特殊原因
        if (worderPmStimulateVo.getStimulateReason().equals(-1)) {
            worderPmStimulateVo.setReasonType(IntegerEnum.ONE.getValue());
        } else {
            worderPmStimulateVo.setReasonType(IntegerEnum.ZERO.getValue());
        }

        // 计算网点结算费用
        calculateDotTax(worderPmStimulateVo);
        //计算车企结算费用
        calculateCompanyFee(worderPmStimulateVo);

        baseMapper.updateStimulate(worderPmStimulateVo);

        if (worderPmStimulateVo.getFileIds() != null && worderPmStimulateVo.getFileIds().length > 0) {
            for (Integer fileId : worderPmStimulateVo.getFileIds()) {
                worderPmStimulateDao.addStimulateFile(StimulateFileEntity.builder()
                        .stimulateId(worderPmStimulateVo.getId())
                        .fileId(fileId).build());
            }
        }
    }

    /**
     * 计算车企结算费用
     *
     * @param worderPmStimulateVo
     */
    private void calculateCompanyFee(WorderPmStimulateVo worderPmStimulateVo) {

        BigDecimal stimulateFee = worderPmStimulateVo.getStimulateFee();

        //根据工单信息中的模板id获取模板信息，再根据信息的车企结算规则id获取规则信息中的税点
        Integer worderId = worderPmStimulateVo.getWorderId();
        WorderInformationEntity worderInformation = this.getById(worderId);
        Integer templateId = worderInformation.getTemplateId();
        //根据模板id获取模板信息中的车企结算规则id
        WorderTemplateDto templateInfoById = worderTemplateDao.findTemplateInfoById(templateId);
        templateInfoById.getWorderAutoCompanyBalanceId();
        QueryWrapper<BalanceRuleEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("id", templateInfoById.getWorderAutoCompanyBalanceId());
        //查询规则对应的税点字段值，去字典表查询对应税点
        BalanceRuleEntity balanceRuleEntity1 = balanceRuleService.getBaseMapper().selectOne(wrapper);
        Integer balanceTaxRate = balanceRuleEntity1.getBalanceTaxRate();
        SysDictionaryDetailEntity sysDictionaryDetailEntity = sysDictionaryDetailService.selectByIdAndNumber(45, Integer.toString(balanceTaxRate));
        String detailName = sysDictionaryDetailEntity.getDetailName();
        BigDecimal companyTaxRate = new BigDecimal(detailName);

        int roundMode = balanceProperties.ROUND_MODE;
        // 税费
        BigDecimal stimulateFeeTax;
        // 含税价
        BigDecimal stimulateFeeSum;
        // 含税价
        if (worderPmStimulateVo.getStimulateFee() != null && worderPmStimulateVo.getPriceType() != null && StringUtils.equals(worderPmStimulateVo.getStimulateType(), "11")) {
            if (worderPmStimulateVo.getPriceType() == 0) {
                stimulateFeeSum = stimulateFee;
                stimulateFeeTax = stimulateFeeSum.divide(companyTaxRate.add(BigDecimal.ONE), 10, roundMode).multiply(companyTaxRate).setScale(2, roundMode);
                stimulateFee = stimulateFeeSum.subtract(stimulateFeeTax);
            } else {
                stimulateFeeTax = stimulateFee.multiply(companyTaxRate).setScale(2, roundMode);
                stimulateFeeSum = stimulateFee.add(stimulateFeeTax);
            }
            worderPmStimulateVo.setBalanceFee(stimulateFee);
            worderPmStimulateVo.setBalanceFeeTax(stimulateFeeSum);
            worderPmStimulateVo.setFeeTax(stimulateFeeTax);
            worderPmStimulateVo.setTaxPoint("%" + (companyTaxRate.multiply(new BigDecimal(100)).intValue()));
        }

    }

    /**
     * 计算网点结算费用
     *
     * @param worderPmStimulateVo
     */
    private void calculateDotTax(WorderPmStimulateVo worderPmStimulateVo) {
        if (worderPmStimulateVo.getDotId() != null) {
            String taxPoint = dotInformationDao.getDotTaxPointByDotId(worderPmStimulateVo.getDotId());
            if (StringUtils.isBlank(taxPoint)) {
                return;
            }

            if (worderPmStimulateVo.getStimulateFee() != null && worderPmStimulateVo.getPriceType() != null && StringUtils.equals(worderPmStimulateVo.getStimulateType(), "10")) {
                worderPmStimulateVo.setTaxPoint(taxPoint);
                BigDecimal taxPointBigDecimal = new BigDecimal(taxPoint.replaceAll("%", "").trim());
                int roundMode = balanceProperties.ROUND_MODE;
                BigDecimal taxRate = taxPointBigDecimal.divide(new BigDecimal(100), 10, roundMode);
                switch (worderPmStimulateVo.getPriceType()) {

                    // 含税价
                    case 0:
                        worderPmStimulateVo.setBalanceFeeTax(worderPmStimulateVo.getStimulateFee());
                        worderPmStimulateVo.setFeeTax(worderPmStimulateVo.getStimulateFee().divide(taxRate.add(BigDecimal.ONE), 10, roundMode).multiply(taxRate).setScale(2, roundMode));
                        worderPmStimulateVo.setBalanceFee(worderPmStimulateVo.getBalanceFeeTax().subtract(worderPmStimulateVo.getFeeTax()));
                        break;
                    // 不含税价
                    case 1:

                        worderPmStimulateVo.setBalanceFee(worderPmStimulateVo.getStimulateFee());
                        worderPmStimulateVo.setFeeTax(worderPmStimulateVo.getStimulateFee().multiply(taxRate.setScale(2, roundMode)));
                        worderPmStimulateVo.setBalanceFeeTax(worderPmStimulateVo.getFeeTax().add(worderPmStimulateVo.getBalanceFee()));

                        break;
                    default:
                        return;
                }
//                }
            }
        }
    }

    /**
     * 奖惩对象类型获取`
     */
    @Override
    public List<Map<String, Object>> listStimulateType() {
        return baseMapper.getStimulateType();
    }

    /**
     * 奖惩类型，即激励原因获取
     */
    @Override
    public List<Map<String, Object>> listStimulateReason(String dicName) {
        List<Map<String, Object>> list = baseMapper.listStimulateReason(dicName);

        SysUserEntity user = (SysUserEntity) SecurityUtils.getSubject().getPrincipal();

        // 销售角色返回所有激励原因
        if (user.getRoleName().indexOf("销售") >= 0) {
            return list;
        }
        // 非销售角色过滤特殊原因
        List<Map<String, Object>> reasonList = new ArrayList<>();

        for (Map<String, Object> map : list) {
            if ("-1".equals(map.get("stimulateReason"))) {
                continue;
            }
            reasonList.add(map);
        }

        return reasonList;
    }

    @Override
    public R getStimulateReason() {
        Map<String, List<Map<String, Object>>> map = new HashMap<String, List<Map<String, Object>>>() {
            {
                put("incentive_dot_reason", baseMapper.getStimulateDotReason());
                put("incentive_reason", baseMapper.getStimulateReason());
                put("negative_incentive_dot_reason", baseMapper.getStimulateDotNegativeReason());
                put("negative_incentive_reason", baseMapper.getStimulateNegativeReason());
            }
        };
        return R.ok().put("data", map);
    }

    /**
     * 激励列表查询
     *
     * @param
     * @return
     */
    @Override
    public R getStimulateList(WorderPmStimulateVo worderPmStimulateVo) {
        worderPmStimulateVo.init();

        SysUserEntity user = (SysUserEntity) SecurityUtils.getSubject().getPrincipal();

        Long userId = user.getUserId();

        List<SysRoleEntity> userRolelist = sysRoleService.getByUserId(userId);

        List<WorderPmStimulateVo> list;
        Integer totalCount;
        //如果当前用户是网点管理员，只能查看网点的激励单，当前网点的激励单，查看二审通过的激励单，
        if (UserRoleUtils.isBranch(userRolelist)) {
            //获取网点Id
            Integer userDotId = baseMapper.getUserDotId(userId);
            //如果没有网点，返回null
            if (userDotId == null) {
                return R.ok().put("list", null).put("totalCount", null);
            }
            worderPmStimulateVo.setDotId(userDotId);
            list = baseMapper.getStimulateToIstrator(worderPmStimulateVo);
            totalCount = baseMapper.getStimulateCountToIstrator(worderPmStimulateVo);
        } else {
            // 车企奖惩对象固定为日日顺
            if (StringUtils.isNotBlank(worderPmStimulateVo.getStimulateTypeName()) && "日日顺".equals(worderPmStimulateVo.getStimulateTypeName().trim())) {
                worderPmStimulateVo.setStimulateTypeName("");
                worderPmStimulateVo.setStimulateType("11");
            }

            if (UserRoleUtils.isAdmin(userRolelist)) {
            } else if (UserRoleUtils.isCustomerService(userRolelist) || UserRoleUtils.isCompanyManager(userRolelist)) {//客服或者厂商管理员
                worderPmStimulateVo.setCreateBy(userId);
            } else if (UserRoleUtils.isPM(userRolelist)) {//项目经理
                worderPmStimulateVo.setPmId(userId);
            } else if (UserRoleUtils.isService(userRolelist)) {//服务兵
                worderPmStimulateVo.setServiceId(userId);
            } else {
                List<ManagerAreaBrandDto> userAreaBrand = baseMapper.getUserAreaBrand(user.getUserId());
                if (!CollectionUtils.isEmpty(userAreaBrand)) {
                    List<Integer> areaIds = userAreaBrand.stream().filter(o -> o.getAreaId() != null).map(ManagerAreaBrandDto::getAreaId).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(areaIds)) {
                        worderPmStimulateVo.setQueryUserId(userId);
                    }
                }
            }

            list = baseMapper.getStimulateList(worderPmStimulateVo);
            totalCount = baseMapper.getStimulateCount(worderPmStimulateVo);
        }
        Map<String, List<Map<String, Object>>> map = new HashMap<String, List<Map<String, Object>>>() {
            {
                put("100", baseMapper.getStimulateDotReason());
                put("110", baseMapper.getStimulateReason());
                put("101", baseMapper.getStimulateDotNegativeReason());
                put("111", baseMapper.getStimulateNegativeReason());
            }
        };
        list.stream().forEach(o -> {
            Optional<Map<String, Object>> reason = map.get(o.getStimulateType() + "" + o.getIncentiveType()) == null || map.get(o.getStimulateType() + "" + o.getIncentiveType()).size() == 0 ? null : map.get(o.getStimulateType() + "" + o.getIncentiveType()).stream()
                    .filter(item -> Integer.valueOf(item.get("stimulateReason").toString()).equals(o.getStimulateReason()))
                    .findFirst();
            o.setStimulateReasonValue(String.valueOf(reason.isPresent() ? reason.get().get("stimulateReasonName") : ""));

            if (null != o.getStatus() && o.getStatus() == 10) {
                o.setStimulateStatus("待审核");
            } else if (null != o.getStatus() && o.getStatus() == 11) {
                o.setStimulateStatus("已退回");
            } else if (null != o.getStatus() && o.getStatus() == 12) {
                o.setStimulateStatus("待结算");
            } else if (null != o.getStatus() && o.getStatus() == 13) {
                o.setStimulateStatus("已发布");
            } else if (null != o.getStatus() && o.getStatus() == 14) {
                o.setStimulateStatus("已结算");
            } else if (null != o.getStatus() && o.getStatus() == 15) {
                o.setStimulateStatus("已通过");
            } else if (null != o.getStatus() && o.getStatus() == 16) {
                o.setStimulateStatus("不通过");
            } else if (null != o.getStatus() && o.getStatus() == 17) {
                o.setStimulateStatus("激励首次审核通过");
            } else if (null != o.getStatus() && o.getStatus() == 18) {
                o.setStimulateStatus("激励二次审核通过");
            } else if (null != o.getStatus() && o.getStatus() == 19) {
                o.setStimulateStatus("激励三次审核通过");
            } else if (null != o.getStatus() && o.getStatus() == 20) {
                o.setStimulateStatus("发布审核不通过");
            } else if (null != o.getStatus() && o.getStatus() == 21) {
                o.setStimulateStatus("等待记账");
            } else if (null != o.getStatus() && o.getStatus() == 22) {
                o.setStimulateStatus("记账成功");
            } else if (null != o.getStatus() && o.getStatus() == 30) {
                o.setStimulateStatus("手动推送5A发布");
            } else if (null != o.getStatus() && o.getStatus() == 31) {
                o.setStimulateStatus("等待开票");
            } else if (null != o.getStatus() && o.getStatus() == 32) {
                o.setStimulateStatus("车企结算发布中");
            } else if (null != o.getStatus() && o.getStatus() == 33) {
                o.setStimulateStatus("发布第一次审核通过");
            }
        });
        return R.ok().put("list", list).put("page", worderPmStimulateVo.getPage()).put("pageSize", worderPmStimulateVo.getPageSize()).put("totalCount", totalCount);

    }

    @Override
    public List<WorderPmStimulateExportVo> getExportStimulateList(WorderPmStimulateVo worderPmStimulateVo) {
        SysUserEntity user = (SysUserEntity) SecurityUtils.getSubject().getPrincipal();

        Long userId = user.getUserId();

        List<SysRoleEntity> userRolelist = sysRoleService.getByUserId(userId);

        List<WorderPmStimulateExportVo> list;
        Integer totalCount;
        //如果当前用户是网点管理员，只能查看网点的激励单，当前网点的激励单，查看二审通过的激励单，
        if (UserRoleUtils.isBranch(userRolelist)) {
            //获取网点Id
            Integer userDotId = baseMapper.getUserDotId(userId);
            //如果没有网点，返回null
            if (userDotId == null) {
                return new ArrayList<WorderPmStimulateExportVo>();
            }
            worderPmStimulateVo.setDotId(userDotId);
            list = baseMapper.getStimulateToIstratorNoLimit(worderPmStimulateVo);
        } else {
            // 车企奖惩对象固定为日日顺
            if (StringUtils.isNotBlank(worderPmStimulateVo.getStimulateTypeName()) && "日日顺".equals(worderPmStimulateVo.getStimulateTypeName().trim())) {
                worderPmStimulateVo.setStimulateTypeName("");
                worderPmStimulateVo.setStimulateType("11");
            }

            if (UserRoleUtils.isAdmin(userRolelist)) {
            } else if (UserRoleUtils.isCustomerService(userRolelist) || UserRoleUtils.isCompanyManager(userRolelist)) {//客服或者厂商管理员
                worderPmStimulateVo.setCreateBy(userId);
            } else if (UserRoleUtils.isPM(userRolelist)) {//项目经理
                worderPmStimulateVo.setPmId(userId);
            } else if (UserRoleUtils.isService(userRolelist)) {//服务兵
                worderPmStimulateVo.setServiceId(userId);
            } else {
                List<ManagerAreaBrandDto> userAreaBrand = baseMapper.getUserAreaBrand(user.getUserId());
                if (!CollectionUtils.isEmpty(userAreaBrand)) {
                    List<Integer> areaIds = userAreaBrand.stream().filter(o -> o.getAreaId() != null).map(ManagerAreaBrandDto::getAreaId).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(areaIds)) {
                        worderPmStimulateVo.setQueryUserId(userId);
                    }
                }
            }

            list = baseMapper.getStimulateListNoLimit(worderPmStimulateVo);
        }
        Map<String, List<Map<String, Object>>> map = new HashMap<String, List<Map<String, Object>>>() {
            {
                put("100", baseMapper.getStimulateDotReason());
                put("110", baseMapper.getStimulateReason());
                put("101", baseMapper.getStimulateDotNegativeReason());
                put("111", baseMapper.getStimulateNegativeReason());
            }
        };
        list.stream().forEach(o -> {
            Optional<Map<String, Object>> reason = map.get(o.getStimulateType() + "" + o.getIncentiveType()) == null || map.get(o.getStimulateType() + "" + o.getIncentiveType()).size() == 0 ? null : map.get(o.getStimulateType() + "" + o.getIncentiveType()).stream()
                    .filter(item -> Integer.valueOf(item.get("stimulateReason").toString()).equals(o.getStimulateReason()))
                    .findFirst();
            o.setStimulateReasonValue(String.valueOf(reason.isPresent() ? reason.get().get("stimulateReasonName") : ""));

            if (null != o.getStatus() && o.getStatus() == 10) {
                o.setStimulateStatus("待审核");
            } else if (null != o.getStatus() && o.getStatus() == 11) {
                o.setStimulateStatus("已退回");
            } else if (null != o.getStatus() && o.getStatus() == 12) {
                o.setStimulateStatus("待结算");
            } else if (null != o.getStatus() && o.getStatus() == 13) {
                o.setStimulateStatus("已发布");
            } else if (null != o.getStatus() && o.getStatus() == 14) {
                o.setStimulateStatus("已结算");
            } else if (null != o.getStatus() && o.getStatus() == 15) {
                o.setStimulateStatus("已通过");
            } else if (null != o.getStatus() && o.getStatus() == 16) {
                o.setStimulateStatus("不通过");
            } else if (null != o.getStatus() && o.getStatus() == 17) {
                o.setStimulateStatus("激励首次审核通过");
            } else if (null != o.getStatus() && o.getStatus() == 18) {
                o.setStimulateStatus("激励二次审核通过");
            } else if (null != o.getStatus() && o.getStatus() == 19) {
                o.setStimulateStatus("激励三次审核通过");
            } else if (null != o.getStatus() && o.getStatus() == 20) {
                o.setStimulateStatus("发布审核不通过");
            } else if (null != o.getStatus() && o.getStatus() == 21) {
                o.setStimulateStatus("等待记账");
            } else if (null != o.getStatus() && o.getStatus() == 22) {
                o.setStimulateStatus("记账成功");
            } else {
                o.setStimulateStatus(o.getStatus() + "");
            }
        });
        return list;

    }

    @Override
    public List<WorderPmStimulateExportVo> getExportStimulateListFromSlave(WorderPmStimulateVo worderPmStimulateVo) {
        Class<?> threadClazz = null;
        try {
            threadClazz = Class.forName("com.youngking.renrenwithactiviti.datasource.config.DynamicContextHolder");
            Method method1 = threadClazz.getMethod("peek");
            method1.invoke(null, null);
            Method method = threadClazz.getMethod("push", String.class);
            method.invoke(null, "slave1");
            return this.getExportStimulateList(worderPmStimulateVo);
        } catch (Exception e) {
            log.error("exception: getExportStimulateListFromSlave, {}", com.alibaba.fastjson.JSON.toJSONString(worderPmStimulateVo), e);
        } finally {
            try {
                Method method1 = threadClazz.getMethod("poll");
                Object invoke = method1.invoke(null, null);
            } catch (Exception e) {
                log.error("error in finally", e);
            }
        }
        return Collections.emptyList();
    }

    /**
     * 撤销激励
     *
     * @param worderPmStimulateVo
     * @return
     */
    @Override
    public R cancelStimulate(WorderPmStimulateVo worderPmStimulateVo) {
        try {
            baseMapper.updateStimulateCancel(worderPmStimulateVo);
        } catch (Exception e) {
            return R.error(e.toString());
        }
        return R.ok();
    }

    /**
     * 获取激励详情信息
     *
     * @return
     */
    @Override
    public WorderPmStimulateVo getStimulateInfo(String stimulateId) {
        WorderPmStimulateVo worderPmStimulateVo = baseMapper.getStimulateInfo(stimulateId);
        if (worderPmStimulateVo != null) {
            List<StimulateFileEntity> stimulateFileEntityList = worderPmStimulateDao.findStimulateFileByStimulateId(worderPmStimulateVo.getId());
            if (stimulateFileEntityList != null && stimulateFileEntityList.size() > 0) {
                List<Integer> fileIds = stimulateFileEntityList.stream().map(StimulateFileEntity::getFileId).collect(Collectors.toList()); //获取文件ID
                List<BalanceFileEntity> fileList = (List<BalanceFileEntity>) balanceFileService.listByIds(fileIds);
                worderPmStimulateVo.setFileList(fileList);
            }
        }
        return worderPmStimulateVo;
    }

    @Override
    public Integer queryStimulateByIdAndStatus(Integer id, Integer status) {
        return baseMapper.queryStimulateByIdAndStatus(id, status);
    }

    @Override
    public Integer queryPublishByBatchNoAndStatus(String batchNo, Integer batchStatus) {
        return baseMapper.queryPublishByBatchNoAndStatus(batchNo, batchStatus);
    }

    /**
     * 修改激励状态
     */
    @Override
    @Transactional
    public void updateStimulateStatus(WorderPmStimulateVo worderPmStimulateVo) {
        baseMapper.updateStimulateStatus(worderPmStimulateVo);
        WorderPmStimulateVo stimulateInfo = baseMapper.getStimulateInfo(String.valueOf(worderPmStimulateVo.getId()));

        Map<String, List<Map<String, Object>>> map = new HashMap<String, List<Map<String, Object>>>() {
            {
                put("100", baseMapper.getStimulateDotReason());
                put("110", baseMapper.getStimulateReason());
                put("101", baseMapper.getStimulateDotNegativeReason());
                put("111", baseMapper.getStimulateNegativeReason());
            }
        };
        Optional<Map<String, Object>> reason = map.get(stimulateInfo.getStimulateType() + "" + stimulateInfo.getIncentiveType()) == null || map.get(stimulateInfo.getStimulateType() + "" + stimulateInfo.getIncentiveType()).size() == 0 ? null : map.get(stimulateInfo.getStimulateType() + "" + stimulateInfo.getIncentiveType()).stream()
                .filter(item -> Integer.valueOf(item.get("stimulateReason").toString()).equals(stimulateInfo.getStimulateReason()))
                .findFirst();
        stimulateInfo.setStimulateReasonValue(String.valueOf(reason.isPresent() ? reason.get().get("stimulateReasonName") : ""));

        // 激励二次审核通过
        if (worderPmStimulateVo.getStatus() == 18) {

            WorderPmStimulateEntity worderPmStimulateEntity = new WorderPmStimulateEntity();
            BeanUtils.copyProperties(stimulateInfo, worderPmStimulateEntity);
            worderPmStimulateEntity.setStimulateType(Integer.parseInt(stimulateInfo.getStimulateType()));
            // 拆分结算子订单
            WorderChildInformationEntity worderChildInformationEntity = worderChildInformationService.splitBalanceStimulate(worderPmStimulateEntity);

            if ("11".equals(stimulateInfo.getStimulateType())) {
                // 加入待工单结算-车企待结算
                worderWaitAccountService.addStimulateWaitAccount(worderPmStimulateEntity.getWorderId(), worderPmStimulateEntity.getId());
            } else if (String.valueOf(IntegerEnum.TEN.getValue()).equals(stimulateInfo.getStimulateType())) {
                // 网点激励 推送acs记账
                log.info("---------网点激励推送财务中台---------工单号" + worderPmStimulateVo.getWorderNo());

                List<WorderPmStimulateEntity> stimulateList = new ArrayList<>();
                stimulateList.add(worderPmStimulateEntity);
                List<WorderChildInformationEntity> worderChildInformationList = new ArrayList<>();
                worderChildInformationList.add(worderChildInformationEntity);
                worderInformationAccountService.pushFinanceAccountStimulate(worderChildInformationList, stimulateList);

            }
        }
    }


    /**
     * 客户满意度回访
     *
     * @param clientSatisfactionVo
     */
    @Override
    public void addClientSatisfaction(ClientSatisfactionVo clientSatisfactionVo) {
        //根据工单编号查询
        ClientSatisfactionVo satisfactionByWorderNo = baseMapper.getSatisfactionByWorderNo(clientSatisfactionVo.getWorderNo());
        if (satisfactionByWorderNo != null) {
            satisfactionByWorderNo.setWorderNo(clientSatisfactionVo.getWorderNo());
            satisfactionByWorderNo.setServiceSatisfaction(clientSatisfactionVo.getServiceSatisfaction());
            satisfactionByWorderNo.setTroubleSpot(clientSatisfactionVo.getTroubleSpot());
            satisfactionByWorderNo.setTroubleSpotContent(clientSatisfactionVo.getTroubleSpotContent());
            satisfactionByWorderNo.setSatisfactionScore(clientSatisfactionVo.getSatisfactionScore());
            satisfactionByWorderNo.setCustomerAdvice(clientSatisfactionVo.getCustomerAdvice());
            baseMapper.updateClientSatisfaction(satisfactionByWorderNo);
        } else {
            baseMapper.addClientSatisfaction(clientSatisfactionVo);
        }
    }

    /**
     * 新增工单物料时获取物料名称
     *
     * @return
     */
    public List<Map<String, Object>> listMateriel() {
        return baseMapper.getMateriel();
    }

    /**
     * 根据物料id获取该物料的品牌和规格
     *
     * @param materielId
     * @return
     */
    public Map<String, Object> getMaterielBrandSpec(Integer materielId) {
        Map<String, Object> materielBrandSpec = baseMapper.getMaterielBrand(materielId);
        return materielBrandSpec;
    }

    /**
     * 修改工单物料时的回显
     *
     * @param materielId
     * @param worderId
     * @return
     */
    public Map<String, Object> getMaterielInfo(Integer materielId, Integer worderId) {
        return baseMapper.getMaterielInfo(materielId, worderId);
    }

    /**
     * 修改开关状态
     */
    public void updateSwitchStatus(Integer status) {
        baseMapper.updateSwitchStatus(status, "360_Quota_Config");
    }

    /**
     * 获取开关状态
     *
     * @return
     */
    public Integer getSwitchStatus() {
        Integer switchStatus = baseMapper.getSwitchStatus("360_Quota_Config");
        return switchStatus;
    }

    /**
     * 同步360额度数据到数据库
     */
    @Override
    @Transactional
    public void addGuaranteeQuota() {
        List<CustomerGuaranteeQuotaEntity> quotaList = insuranceQuotaService.getInsuranceAmount();
        if (!CollectionUtils.isEmpty(quotaList)) {
            baseMapper.deleteQuota();
            baseMapper.addQuota(quotaList);
        }

    }

    /**
     * 查询字典值
     *
     * @return
     */
    public List<ExtFieldDictionaryDto> queryStatus(String dicNumber) {
        List<ExtFieldDictionaryDto> dicValue = extFieldDao.findByDicNumber(dicNumber);
        return dicValue;
    }

    /**
     * 勘测结单后续处理-增加物料信息及结算
     *
     * @param worderId
     * @return
     */
    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public R materielBalanceAccounts(Integer worderId) {
        //删除工单相关物料信息
        excuteSqlMapper.deleteWorderUsedMateriel(worderId);
        // 添加工单勘测固定物料
        Integer worderUsedMateriel = excuteSqlMapper.insertWorderUsedMateriel(worderId, 5);
        Integer worderUsedMateriel2 = excuteSqlMapper.insertWorderUsedMateriel2(worderId, 5);
        if (worderUsedMateriel < 1 && worderUsedMateriel2 < 1) {
            return R.error("勘测物料不存在或物料已添加！");
        }

        WorderWaitAccountEntity worderWaitAccount = new WorderWaitAccountEntity();
        worderWaitAccount.setWorderId(worderId);
        worderWaitAccount.setWorderInvoiceType(0);
        worderWaitAccount.setStimulateId(null);
        worderWaitAccount.setGmtCreate(DateUtils.getCurrentTime());
        QueryWrapper<WorderInformationEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("worder_id", worderId);
        WorderInformationEntity worderInformationEntity = this.baseMapper.selectOne(wrapper);
        Integer companyId = worderInformationEntity.getCompanyId();
        worderWaitAccount.setCompanyId(String.valueOf(companyId));

        List<WorderBalanceFeeDetailEntity> balanaceDetailList = new ArrayList<>();
        List<WorderBalanceFeeEntity> worderBalanceList = new ArrayList<>();
        WorderBalanceFeeEntity worderBalanceFeeEntity = new WorderBalanceFeeEntity();
        worderBalanceFeeEntity.setWorderId(worderId);
        worderBalanceFeeEntity.setWorderSetStatus(1);
        worderBalanceFeeEntity.setWorderSetStatusValue("车企待结算");
        worderBalanceFeeEntity.setWorderSetFlag(true);
        worderBalanceList.add(worderBalanceFeeEntity);
        WorderBalanceFeeEntity worderBalanceFee = branchBalanceDao.queryWorderForBalanceByWorderId(worderId);
        if (worderBalanceFee == null) {
            return R.error("未获取到工单结算信息");
        }
        int roundMode = balanceProperties.ROUND_MODE;

        // 套包物料
        Integer suiteId = worderBalanceFee.getSuiteId();
        //List<SuiteDetailEntity> suiteDetailList = suiteDetailDao.selectList(new QueryWrapper<SuiteDetailEntity>()
        //		.eq("suite_id", suiteId));
        //转换成Map，方便快速查找，格式：suiteId -> ( materielId -> num )
        Map<Integer, BigDecimal> suiteMaterialMap = new HashMap<>();
        //	for (SuiteDetailEntity s : suiteDetailList) {
        //		suiteMaterialMap.put(s.getMaterielId(), s.getNum());
        //	}

        Integer companyBalanceRuleId = worderBalanceFee.getCompanyBalanceRuleId();
        // 工单结算规则
        BalanceRuleEntity companyBalanceRule = balanceRuleDao.queryBatchBalanceRuleDetailsByRuleId(companyBalanceRuleId);
        //整理厂商结算规则中每个物料的最大数量
        Map<Integer, BigDecimal> companyRuleMaxMaterielNumMap = branchBalanceService.getBalanceRuleMaxMaterielNumMap(companyBalanceRule);

        Integer priceType = companyBalanceRule.getPriceType();
        Boolean companyFlag = true;
        if (companyFlag) {
            //厂商税率
            BigDecimal companyTaxRate = balanceProperties.RRS_COMPANY_TAX_RATE;
            BigDecimal companyFee = BigDecimal.ZERO;
            // 厂商结算 +套包价格
            //companyFee = branchBalanceService.companySuitBalance(companyBalanceRule, worderId, balanaceDetailList, companyFee, suiteId,
            //		companyTaxRate, roundMode);

            //根据车企结算规则过滤符合条件的结算项
            BalanceRuleUtils.filterBalanceRuleDetails(companyBalanceRule, worderBalanceFee);
            // 厂商结算 +物料价格
            companyFee = branchBalanceService.companyMaterielBalance(companyBalanceRule, worderId, balanaceDetailList, companyFee,
                    companyTaxRate, roundMode, suiteMaterialMap, worderBalanceFee, companyRuleMaxMaterielNumMap);

            /* 计算厂商结算金额和网点结算金额 end */
            //金额规范化，并计算税额和含税价
            companyFee = companyFee.setScale(2, roundMode);
            BigDecimal companyFeeTax = BigDecimal.ZERO;
            BigDecimal companyFeeSum = BigDecimal.ZERO;
            if (priceType == 0) {
                //含税价
                companyFeeSum = companyFee;
                //不含税价
                companyFeeTax = companyFeeSum.divide(companyTaxRate.add(BigDecimal.ONE), 10, roundMode).multiply(companyTaxRate).setScale(2, roundMode);
                //税费
                companyFee = companyFeeSum.subtract(companyFeeTax);
            } else {
                //价格类型不含税价格
                companyFeeTax = companyFee.multiply(companyTaxRate).setScale(2, roundMode);
                //含税价
                companyFeeSum = companyFee.add(companyFeeTax);
            }
            worderWaitAccount.setCompanyBalanceFee(companyFee);
            worderWaitAccount.setCompanyBalanceFeeTax(companyFeeTax);
            worderWaitAccount.setCompanyBalanceFeeSum(companyFeeSum);

            worderWaitAccountService.save(worderWaitAccount);

            worderBalanceFeeEntity.setCompanyBalanceFee(companyFee);
            worderBalanceFeeEntity.setCompanyBalanceFeeSum(companyFeeSum);
            worderBalanceFeeEntity.setCompanyBalanceFeeTax(companyFeeTax);
        }

        BigDecimal dotFee = BigDecimal.ZERO;
        // 网点税率
        BigDecimal dotTaxRate = branchBalanceService.getTaxRateByTaxPoint(worderBalanceFee.getTaxPoint());
        // 网点结算规则
        Integer dotBalanceRuleId = worderBalanceFee.getDotBalanceRuleId();
        BalanceRuleEntity dotBalanceRule = balanceRuleDao.queryBatchBalanceRuleDetailsByRuleId(dotBalanceRuleId);
        // 网点结算
        dotFee = branchBalanceService.dotBalanceFee(dotBalanceRule, worderId, balanaceDetailList, dotFee,
                dotTaxRate, roundMode, suiteMaterialMap, worderBalanceFee, companyRuleMaxMaterielNumMap);

        dotFee = dotFee.setScale(2, roundMode);
        BigDecimal dotFeeTax = dotFee.multiply(dotTaxRate).setScale(2, roundMode);
        BigDecimal dotFeeSum = dotFee.add(dotFeeTax);

        worderBalanceFeeEntity.setWorderId(worderId);
        worderBalanceFeeEntity.setDotBalanceFee(dotFee);
        worderBalanceFeeEntity.setDotBalanceFeeSum(dotFeeSum);
        worderBalanceFeeEntity.setDotBalanceFeeTax(dotFeeTax);
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(worderBalanceList)) {
            branchBalanceDao.batchSaveWorderBalanceFee(worderBalanceList);
        }
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(balanaceDetailList)) {
            worderBalanceFeeDetailService.saveBatch(balanaceDetailList);
        }
        return R.ok("处理成功");
    }

    /**
     * 工单转移
     *
     * @param userTransferVo
     * @return
     */
    @Override
    public R userTransfer(UserTransferVo userTransferVo) {
        SysUserEntity sysUser = getUser();
        try {
            if (null != userTransferVo.getRegionList() && userTransferVo.getRegionList().size() > 0) {
                List<BizRegionEntity> list = bizRegionDao.selectAreaIdByIds(userTransferVo.getRegionList());
                List<Integer> integers = new ArrayList<>();
                if (list.size() > 0 && list != null) {
                    for (BizRegionEntity entity : list) {
                        Integer id = entity.getId().intValue();
                        integers.add(id);
                    }
//                    String s = StringUtils.join(integers, ",");
//                    userTransferVo.setRegions(s);
                }
                userTransferVo.setRegionList(integers);
            }
            List<String> worderNoList = baseMapper.queryWorderNoTransfer(userTransferVo);
            userTransferVo.setWorderNoList(worderNoList);
            baseMapper.userTransfer(userTransferVo);
            if (userTransferVo.getUserType() == 2) {
                if (worderNoList.size() > 0) {
                    //根据品牌等查询有工单
                    List<SuperviseInfomation> idList = baseMapper.querySuperviseTransfer(userTransferVo);
                    //服务经理转单==>转移名下督办单
                    baseMapper.userSuperviseTransfer(userTransferVo);
                    //转移来电记录
                    baseMapper.userSuperviseTeleTransfer(userTransferVo);
                    for (SuperviseInfomation superviseInfomation : idList) {
                        SuperviseOperationRecord superviseOperationRecord = new SuperviseOperationRecord();
                        superviseOperationRecord.setSuperviseId(superviseInfomation.getId());
                        superviseOperationRecord.setRelatedPeo(superviseInfomation.getRelatedPeo());
                        superviseOperationRecord.setDutyPeo(userTransferVo.getToUserId().toString());
                        superviseOperationRecord.setOptrationContent("督办单转移");
                        superviseOperationRecord.setUserId(Integer.valueOf(sysUser.getUserId().toString()));
                        superviseOperationRecord.setUserName(sysUser.getUsername());
                        //循环添加操作记录
                        baseMapper.insertSuperviseOperationRecord(superviseOperationRecord);
                    }
                }
            }
            return R.ok();
        } catch (Exception e) {
            e.printStackTrace();
            return R.error(e.getMessage());
        }
    }

    @Override
    public R incentiveImport(MultipartFile file, Integer fileIds[]) throws IOException {
        SysUserEntity user = getUser();
        //失败列表
        List<String> failureList = new ArrayList<>();
        EasyExcel.read(file.getInputStream(), IncentiveImpDto.class, new IncentiveListenter(worderInformationDao, dotInformationDao, worderPmStimulateDao, user, failureList, fileIds, worderInformationController)).autoCloseStream(true).sheet().doRead();
        if (failureList.size() > 0) {
            return R.error().put("failureMessage", failureList);
        }
        return R.ok();
    }

    @Override
    public R incentiveCarImport(MultipartFile file, Integer fileIds[]) throws IOException {
        SysUserEntity user = getUser();
        //失败列表
        List<String> failureList = new ArrayList<>();
        EasyExcel.read(file.getInputStream(), CarIncentiveImpDto.class, new IncentiveCarListenter(worderInformationDao, dotInformationDao, worderPmStimulateDao, user, failureList, fileIds, worderInformationController)).autoCloseStream(true).sheet().doRead();
        if (failureList.size() > 0) {
            return R.error().put("failureMessage", failureList);
        }
        return R.ok();
    }

    /**
     * 获取未结算开票工单
     *
     * @param worderNo
     * @return
     */
    @Override
    public List<WorderInformationEntity> getNoInvoicedWorder(String worderNo) {
        return worderInformationDao.getNoInvoicedWorder(worderNo);
    }

    @Override
    public R selectWorderInformationByWorderNo(String worderNo) {
        List<WorderInformationEntity> worderInformationEntities = this.baseMapper.selectList(new QueryWrapper<WorderInformationEntity>().eq("worder_no", worderNo));
        if (worderInformationEntities != null && worderInformationEntities.size() > 0) {
            WorderInformationEntity worderInformationEntity = worderInformationEntities.get(0);
            Integer worderStatus = worderInformationEntity.getWorderStatus();
            if (worderStatus == 0 || worderStatus == 1 || worderStatus == 2) {
                return R.ok().put("worderStatus", worderStatus);
            } else {
                return R.error(201, "工单号[" + worderNo + "]为车企已确认工单！").put("worderStatus", worderStatus);
            }
        }
        return R.error(201, "工单号[" + worderNo + "]未查询到工单信息！");
    }

    @Override
    public boolean checkBanlanRule(Integer worderId) {
        //查询工单信息
        WorderInformationEntity worderInformation = this.baseMapper.selectById(worderId);

        // 查询对应的工单模板
        WorderTemplateDto templateInfo = worderTemplateDao.findTemplateInfoById(worderInformation.getTemplateId());
        // 运维项目品牌不校验
        if ("44".equals(templateInfo.getBrandId())) {
            return false;
        }

        //判断类型是否为工程
        if (worderInformation.getWorderTypeId() == 7) {
//            查询对应的工单模板
//            WorderTemplateDto templateInfo = worderTemplateDao.findTemplateInfoById(worderInformation.getTemplateId());
            //查询是否匹配到结/算规则
            List<BalanceRuleEntity> balanceRules = balanceRuleDao.selectList(new QueryWrapper<BalanceRuleEntity>().in("id", Long.parseLong(templateInfo.getWorderAutoCompanyBalanceId()), Long.parseLong(templateInfo.getWorderBranchBalanceId())));
            if (balanceRules == null || balanceRules.size() < 1) {
                return true;
            }
            List<Integer> balanceRuleIds = balanceRules.stream().map(BalanceRuleEntity::getId).collect(Collectors.toList());
            //查询对应的详情信息
            Integer count = balanceRuleDetailDao.selectCount(new QueryWrapper<BalanceRuleDetailEntity>().in("rule_id", balanceRuleIds).eq("judge_project", "1").eq("judge_conditions", "1").eq("judge_value", worderInformation.getWorderNo()));
            return count == null || count < 2;
        }
        return false;
    }

    @Override
    public Integer getCompanyPriceTypeByTemplateId(Integer templateId) {
        return worderInformationDao.getCompanyPriceTypeByTemplateId(templateId);
    }

    public Integer updateWorderStatus(String worderNo) {
        return worderInformationDao.updateStatusWorder(worderNo);
    }

    @Override
    public List<Map<Integer, Object>> getListDotName() {
        return dotInformationDao.getListDotName();
    }

    @Override
    public Integer selectBrandByTemp(Integer worderId) {
        return worderInformationDao.selectBrandByTemp(worderId);
    }

    @Override
    public List<Integer> selectBrandsByDot(Integer dotId) {
        return worderInformationDao.selectBrandsByDot(dotId);
    }


    @Override
    public List<Map<Integer, Object>> getAddedMaterielTypeList() {
        return worderInformationDao.getAddedMaterielTypeList();
    }

    @Override
    public R getDotRuleExtFieldList() {
        List<ExtFieldEntity> extFieldList = worderInformationDao.getDotRuleExtFieldList();
        Map<Integer, List<String>> extFieldMap = extFieldList.stream().collect(Collectors.toMap(ExtFieldEntity::getFieldId, e -> {
            String selectData = e.getSelectData();
            if (StringUtils.isBlank(selectData)) {
                return new ArrayList<String>();
            }
            String[] selectDatas = selectData.split(",");
            return Arrays.asList(selectDatas);
        }));
        return R.ok().put("dotRuleExtFieldList", extFieldList).put("dotRuleExtFieldMap", extFieldMap);
    }

    @Override
    public List<Map<Integer, Object>> getDotBalanceRuleMaterielInfo(Integer dotBalanceRuleId) {
        return worderInformationDao.getDotBalanceRuleMaterielInfo(dotBalanceRuleId);
    }

    private void cockpitDataHandleCondition(Map<String, Object> params) {
        Long userId = getUser().getUserId();
        List<Integer> ids = null;
        if (params.get("cockpitList") != null && "qianli".equals(params.get("cockpitList").toString())) {
            QueryWrapper<BrandEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("show_type", 1);
            List<BrandEntity> brandEntities = brandDao.selectList(queryWrapper);
            ids = brandEntities.stream().map(b -> b.getId()).collect(Collectors.toList());
            List<Integer> worderTypes = new ArrayList<>();
            worderTypes.add(1);
            worderTypes.add(2);
            worderTypes.add(4);
            worderTypes.add(5);
            //(不要维修和工程)
            params.put("worderTypes", worderTypes);

        }
        //获取用户角色信息
        List<SysRoleEntity> list = sysRoleService.getByUserId(userId);
        if (!com.youngking.lenmoncore.common.utils.StringUtils.isEmpty(params.get("brandIds"))) {
            String brandIds = Objects.toString(params.get("brandIds"));
            brandIds = brandIds.replace("[", "").replace("]", "").replace("\"", "");
            String[] brandIdArr = brandIds.split(",");
            params.put("brandIds", Arrays.asList(brandIdArr));
        } else {
            if (ids != null) {
                params.put("brandIds", ids);
            }
        }
        //添加每个客服只能看到自己创建的工单
        if (!com.youngking.lenmoncore.common.utils.StringUtils.isEmpty(params.get("regionLabelList"))) {
            String regionLabelList = Objects.toString(params.get("regionLabelList"));
            regionLabelList = regionLabelList.replace("[", "").replace("]", "").replace("\"", "");
            if (!StringUtils.isBlank(regionLabelList) && !"[]".equals(regionLabelList)) {
                String[] areas = regionLabelList.split(",");
                List<String> areaId = new ArrayList<>();
                List<String> area = new ArrayList<>();
                for (int i = 0; i < areas.length; i++) {
                    QueryWrapper<BizRegionEntity> query = new QueryWrapper<>();
                    String id = areas[i];
                    query.eq("id", id);
                    BizRegionEntity bizRegionEntity = bizRegionDao.selectOne(query);
                    if (bizRegionEntity.getType() == 1) {
                        //省
                        area.add(bizRegionEntity.getId().toString());
                    } else if (bizRegionEntity.getType() == 2) {
                        //市
                        QueryWrapper<BizRegionEntity> queryWrapper = new QueryWrapper<>();
                        queryWrapper.eq("pid", bizRegionEntity.getId());
                        List<BizRegionEntity> bizRegionEntities = bizRegionDao.selectList(queryWrapper);
                        for (int j = 0; j < bizRegionEntities.size(); j++) {
                            areaId.add(bizRegionEntities.get(j).getId().toString());
                        }
                    } else if (bizRegionEntity.getType() == 3) {
                        //县
                        areaId.add(bizRegionEntity.getId().toString());
                    }
                }
                if (area != null && area.size() > 0) {
                    params.put("areas", area);
                }
                if (areaId != null && areaId.size() > 0) {
                    params.put("areaIds", areaId);
                }
            }
        }

        Set<String> keys = new HashSet<>(params.keySet());
        for (String k : keys) {
            Object obj = params.get(k);
            if (obj != null && StringUtils.isBlank(obj + "")) {
                params.remove(k);
            }
        }

        if (UserRoleUtils.isAdmin(list)) {
        } else if (UserRoleUtils.isCustomerService(list) || UserRoleUtils.isCompanyManager(list)) {//客服或者厂商管理员
            params.put("createBy", userId);
        } else if (UserRoleUtils.isPM(list)) {//项目经理
            params.put("pmId", userId);
        } else if (UserRoleUtils.isBranch(list)) {//网点
            params.put("dotId", getDotId(userId));
        } else if (UserRoleUtils.isService(list)) {//服务兵
            params.put("serviceId", userId);
        } else {
            params.put("userId", userId);
        }
    }

    private Map<String, Boolean> getCockpitPermissions(Map<String, Object> params) {
        boolean b = params.get("cockpitList") != null;


        Map<String, Boolean> permissions = new HashMap<>();
        // 未派单
        permissions.put("hasFpWpd", false);
        // 服务经理未派单
        permissions.put("hasFpFwjlwpd", false);
        // 网点已接单
        permissions.put("hasKcWdyjd", false);
        // 待勘测预约
        permissions.put("hasKcDkcyy", false);
        // 待勘测
        permissions.put("hasKcDkc", false);
        // 待安装预约
        permissions.put("hasAzDazyy", false);
        // 待安装
        permissions.put("hasAzDaz", false);
        if (b) {
            // 等待充电桩及备件
            permissions.put("hasAzDdcdzjpj", false);
        }

        // 勘测资料未提交
        permissions.put("hasKczlKczlwtj", false);
        // 勘测资料提交待审核
        permissions.put("hasKczlKczltjdsh", false);
        // 勘测资料整改中
        permissions.put("hasKczlKczlzgz", false);
        // 待客服确认
        permissions.put("hasKczlDkfqr", false);
        if (b) {

        }

        // 安装资料未提交
        permissions.put("hasAzzlAzzlwtj", false);
        // 安装资料已提交待审核
        permissions.put("hasAzzlAzzlytjdsh", false);
        if (b) {

        }
        // 安装资料整改中
        permissions.put("hasAzzlAzzlzgz", false);
        // 安装资料待客服审核
        permissions.put("hasAzzlAzzldkfsh", false);
        // 安装资料无误待上传车企
        permissions.put("hasAzzlAzzlwwdsccq", false);


        SysUserEntity user = getUser();
        // 用户角色id
        List<Long> userRoleIdList = getUser().getRoleIdList();

        // 查询字典配置权限
        List<SysDictionaryDetailEntity> listPermissionDictionary = worderInformationDao.queryCockpitPermission();

        // 校验权限
        verifyPermissions(userRoleIdList, listPermissionDictionary, permissions);
        return permissions;
    }

    @Override
    public List<ExportCSCockpitDataVo> exportCSCockpitData(Map<String, Object> params) {
        cockpitDataHandleCondition(params);

        List<ExportCSCockpitDataVo> list = worderInformationDao.queryExportCSCockpitData(params);
        return list;
    }

    @Override
    public List<CSCockpitVo> queryCSCockpitListData(Map<String, Object> params) {

        cockpitDataHandleCondition(params);

        List<CSCockpitVo> csCockpitVoList = worderInformationDao.queryCSCockpitListData(params);

        for (CSCockpitVo csCockpitVo : csCockpitVoList) {

            csCockpitVo.setWpdCsl(computeTimeoutRate(csCockpitVo.getWpdPdl(), csCockpitVo.getWpdCspdl()));

            csCockpitVo.setFwjlwpdCsl(computeTimeoutRate(csCockpitVo.getFwjlwpdPdl(), csCockpitVo.getFwjlwpdCspdl()));

            csCockpitVo.setWdyjdCsl(computeTimeoutRate(csCockpitVo.getWdyjdPdl(), csCockpitVo.getWdyjdCspdl()));

            csCockpitVo.setDkcyyCsl(computeTimeoutRate(csCockpitVo.getDkcyyPdl(), csCockpitVo.getDkcyyCspdl()));

            csCockpitVo.setDkcCsl(computeTimeoutRate(csCockpitVo.getDkcPdl(), csCockpitVo.getDkcCspdl()));

            csCockpitVo.setDazyyCsl(computeTimeoutRate(csCockpitVo.getDazyyPdl(), csCockpitVo.getDazyyCspdl()));

            csCockpitVo.setDazCsl(computeTimeoutRate(csCockpitVo.getDazPdl(), csCockpitVo.getDazCspdl()));

            csCockpitVo.setFwgcPdl(sumPdl(
                    csCockpitVo.getWpdPdl(),
                    csCockpitVo.getFwjlwpdPdl(),
                    csCockpitVo.getWdyjdPdl(),
                    csCockpitVo.getDkcyyPdl(),
                    csCockpitVo.getDkcPdl(),
                    csCockpitVo.getDazyyPdl(),
                    csCockpitVo.getDazPdl()
            ));
            csCockpitVo.setFwgcCspdl(sumPdl(
                    csCockpitVo.getWpdCspdl(),
                    csCockpitVo.getFwjlwpdCspdl(),
                    csCockpitVo.getWdyjdCspdl(),
                    csCockpitVo.getDkcyyCspdl(),
                    csCockpitVo.getDkcCspdl(),
                    csCockpitVo.getDazyyCspdl(),
                    csCockpitVo.getDazCspdl()
            ));
            csCockpitVo.setFwgcCsl(computeTimeoutRate(csCockpitVo.getFwgcPdl(), csCockpitVo.getFwgcCspdl()));

            csCockpitVo.setKczlwtjCsl(computeTimeoutRate(csCockpitVo.getKczlwtjPdl(), csCockpitVo.getKczlwtjCspdl()));

            csCockpitVo.setAzzlwtjCsl(computeTimeoutRate(csCockpitVo.getAzzlwtjPdl(), csCockpitVo.getAzzlwtjCspdl()));

            csCockpitVo.setAzzlzgzCsl(computeTimeoutRate(csCockpitVo.getAzzlzgzPdl(), csCockpitVo.getAzzlzgzCspdl()));

            csCockpitVo.setAzzldkfshCsl(computeTimeoutRate(csCockpitVo.getAzzldkfshPdl(), csCockpitVo.getAzzldkfshCspdl()));

            csCockpitVo.setZlgcPdl(sumPdl(
                    csCockpitVo.getFwgcPdl(),
                    csCockpitVo.getKczlwtjPdl(),
                    csCockpitVo.getAzzlwtjPdl(),
                    csCockpitVo.getAzzlzgzPdl(),
                    csCockpitVo.getAzzldkfshPdl()
            ));
            csCockpitVo.setZlgcCspdl(sumPdl(
                    csCockpitVo.getFwgcCspdl(),
                    csCockpitVo.getKczlwtjCspdl(),
                    csCockpitVo.getAzzlwtjCspdl(),
                    csCockpitVo.getAzzlzgzCspdl(),
                    csCockpitVo.getAzzldkfshCspdl()
            ));
            csCockpitVo.setZlgcCsl(computeTimeoutRate(csCockpitVo.getZlgcPdl(), csCockpitVo.getZlgcCspdl()));
        }

        return csCockpitVoList;
    }

    private String sumPdl(String... pdls) {
        Integer pdl = 0;
        for (String p : pdls) {
            if (StringUtils.isNotBlank(p)) {
                pdl += Integer.valueOf(p);
            }
        }
        return pdl.toString();
    }

    private String computeTimeoutRate(String pdl, String csPdl) {

        if (StringUtils.isBlank(pdl) || StringUtils.isBlank(csPdl)) {
            return "0%";
        }
        BigDecimal bigPdl = new BigDecimal(Integer.valueOf(pdl));
        BigDecimal bigCsPdl = new BigDecimal(Integer.valueOf(csPdl));
        BigDecimal timeoutRate = BigDecimal.ZERO;
        if (bigPdl.compareTo(BigDecimal.ZERO) > 0 && bigCsPdl.compareTo(BigDecimal.ZERO) > 0) {
            timeoutRate = bigCsPdl.divide(bigPdl, 4, RoundingMode.HALF_EVEN).multiply(BigDecimal.valueOf(100L));
        }
        return timeoutRate.doubleValue() + "%";
    }

    @Override
    public List<Map<String, Object>> queryCockpitData(Map<String, Object> params) {
        Map<String, Boolean> permissions = getCockpitPermissions(params);
        cockpitDataHandleCondition(params);
        // 查询运营工单数据
        Map<String, Object> cockpitDataMap = null;
        Map<String, Object> cockpitCSDataMap = null;
        Map<String, Object> cockpitJRDataMap = null;
        Map<String, Object> cockpitMRDataMap = null;
        Map<String, Object> cockpitHRDataMap = null;
        // 使用CompletableFuture来异步执行查询
        CompletableFuture<Map<String, Object>> f1 = CompletableFuture.supplyAsync(() -> {
            return worderInformationDao.queryCockpitData(params);
        }, executorService);
        CompletableFuture<Map<String, Object>> csFuture = CompletableFuture.supplyAsync(() -> {
            return worderInformationDao.queryCockpitCSData(params);
        });

        CompletableFuture<Map<String, Object>> jrFuture = CompletableFuture.supplyAsync(() -> {
            return worderInformationDao.queryCockpitJRData(params);
        });

        CompletableFuture<Map<String, Object>> mrFuture = CompletableFuture.supplyAsync(() -> {
            return worderInformationDao.queryCockpitMRData(params);
        });

        CompletableFuture<Map<String, Object>> hrFuture = CompletableFuture.supplyAsync(() -> {
            return worderInformationDao.queryCockpitHRData(params);
        });
// 等待所有异步操作完成，并合并结果
        CompletableFuture<Void> combinedFuture = CompletableFuture.allOf(f1, csFuture, jrFuture, mrFuture, hrFuture);
        // 获取所有结果
        try {
            combinedFuture.get(); // 等待所有异步操作完成
            cockpitDataMap = f1.get();
            cockpitCSDataMap = csFuture.get(); // 获取CS数据
            cockpitJRDataMap = jrFuture.get(); // 获取JR数据
            cockpitMRDataMap = mrFuture.get(); // 获取MR数据
            cockpitHRDataMap = hrFuture.get(); // 获取HR数据
            return getCockpitDataList(cockpitDataMap, cockpitCSDataMap, cockpitJRDataMap, cockpitMRDataMap, cockpitHRDataMap, permissions, params);

        } catch (InterruptedException | ExecutionException e) {
            return null;
            // 处理异常情况
        }
    }

    @Override
    public R queryCockpitDetailData2(Map<String, Object> params) {
        Object queryType = params.get("queryType");
        if (queryType == null || StringUtils.isBlank(queryType.toString())) {
            return R.error("queryType不能为空!");
        }

        cockpitDataHandleCondition(params);

        List<CockpitDataStatistics2Dto> cockpitDataStatistics2DtoList = worderInformationDao.queryCockpitDataDetail2(params);

        return getCockpitDataDetail2List(cockpitDataStatistics2DtoList);
    }

    @Override
    //  @Lock4j(keys = "#worderNo")
    public Results goAutoSendWorder(String worderNo, String username, String conveyWorderNo) {
        log.info("工单号：{}创建成功，开始执行自动派单逻辑..", worderNo);
        try {
            if (com.youngking.lenmoncore.common.utils.StringUtils.isNotBlank(conveyWorderNo)) {
                //安装勘测工单自动派单逻辑
                Boolean flag = autoSendService.autoSendInstallToConveyDot(username, worderNo, conveyWorderNo);
                if (flag) {
                    log.info("工单：{}，conveyWorderNo：{} 自动派单处理成功。。", worderNo, conveyWorderNo);
                }
            }
            username = username.replace(" ", "");
            worderNo = worderNo.replace(" ", "");

            Results results = autoSendService.autoSendMagrDot(username, worderNo);
            log.info("工单：{}，自动派单处理结果：{}", worderNo, JSON.toJSONString(results));
            return results;
        } catch (Exception e) {
            log.error("工单：" + worderNo + "，自动派单处理结果", e);
            return Results.message(10, "派单失败", null);
        }
    }

    @Override
    public ClientSatisfactionVo getSatisfactionByWorderNo(String worderNo) {
        return baseMapper.getSatisfactionByWorderNo(worderNo);
    }

    @Override
    @Transactional
    public R cancelAuditReviewFailed(Integer worderId, String content) {
        if (worderId == null) {
            return R.error("工单不合法");
        }
        // 查询工单
        LambdaQueryWrapper<WorderInformationEntity> worderInformationWrapper = Wrappers.lambdaQuery();
        worderInformationWrapper.eq(WorderInformationEntity::getWorderId, worderId);
        WorderInformationEntity worderInformationEntity = worderInformationDao.selectOne(worderInformationWrapper);
        if (worderInformationEntity == null) {
            return R.error("工单不合法");
        }
        // 获取登陆用户
        SysUserEntity user = getUser();
        if (user == null) {
            return R.error("登陆失效，请重新登陆");
        }
        // 调用服务商取消审核信息回传
        Result result = null;
        if (worderInformationEntity.getWorderTypeId() == 8) {
            result = businessProcessPushCheckCancelReviewInfo.callBusinessProcess(BusinessProcessPo.builder().worderId(worderInformationEntity.getWorderId()).worderNo(worderInformationEntity.getWorderNo()).auditType("2").auditRemark(content).build());
        } else {
            result = businessProcessPushCancelReviewInfo.callBusinessProcess(BusinessProcessPo.builder().worderId(worderInformationEntity.getWorderId()).worderNo(worderInformationEntity.getWorderNo()).auditType("2").auditRemark(content).build());
        }
        if (result.getCode() != 0) {
            return R.error(result.getMsg());
        }

        // 清除取消申请标识
        LambdaQueryWrapper<WorderInformationAttributeEntity> worderInformationAttributeWrapper = Wrappers.lambdaQuery();
        worderInformationAttributeWrapper.eq(WorderInformationAttributeEntity::getWorderId, worderId);
        worderInformationAttributeWrapper.eq(WorderInformationAttributeEntity::getAttributeCode, "CPIMCancelOrder");
        worderInformationAttributeDao.delete(worderInformationAttributeWrapper);
        // 记录操作日志并且，记录审核日志
        // 保存操作记录
        WorderOperationRecodeEntity worderOperationRecodeEntity = new WorderOperationRecodeEntity();
        worderOperationRecodeEntity.setUserId(user.getUserId());
        worderOperationRecodeEntity.setOperationUser(user.getEmployeeName());
        worderOperationRecodeEntity.setRecord(user.getEmployeeName() + "：比亚迪工单取消审核不通过,原因：" + content);
        worderOperationRecodeEntity.setWorderNo(worderInformationEntity.getWorderNo());
        worderOperationRecodeEntity.setCreateTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
        worderOperationRecodeEntity.setWorderStatus(String.valueOf(worderInformationEntity.getWorderStatus()));
        worderOperationRecodeEntity.setWorderExecStatus(String.valueOf(worderInformationEntity.getWorderExecStatus()));
        worderOperationRecodeDao.insert(worderOperationRecodeEntity);
        // 保存审核记录
        WorderAuditResultEntity worderAuditResultEntity = new WorderAuditResultEntity();
        worderAuditResultEntity.setWorderNo(worderInformationEntity.getWorderNo());
        worderAuditResultEntity.setWorderStatus(worderInformationEntity.getWorderStatus());
        worderAuditResultEntity.setAuditUserId(user.getUserId());
        worderAuditResultEntity.setWorderAuditStatus(22);
        worderAuditResultEntity.setResult(user.getEmployeeName() + "：比亚迪工单取消审核不通过,原因：" + content);
        worderAuditResultEntity.setGmtCreate(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
        worderAuditResultService.save(worderAuditResultEntity);
        return R.ok();
    }

    @Override
    @Transactional
    public R companyFallback(Integer worderId, String content) {
        if (worderId == null) {
            return R.error("工单不合法");
        }
        // 查询工单
        LambdaQueryWrapper<WorderInformationEntity> worderInformationWrapper = Wrappers.lambdaQuery();
        worderInformationWrapper.eq(WorderInformationEntity::getWorderId, worderId);
        WorderInformationEntity worderInformationEntity = worderInformationDao.selectOne(worderInformationWrapper);
        if (worderInformationEntity == null) {
            return R.error("工单不合法");
        }
        // 获取登陆用户
        SysUserEntity user = getUser();
        if (user == null) {
            return R.error("登陆失效，请重新登陆");
        }

        // 调用流程结束
        if (flowCommon.hasFlowByWorderNo(worderInformationEntity.getWorderNo())) {
            //调用网点已接单流程
            ExecuteFlowResultPo executeFlowResultPo = flowCommon.executeFlow(worderInformationEntity.getWorderNo(), FlowConstant.ProcessCode.StatusUpdate, FlowConstant.ProcessStatus.Y);
            // 流程调用失败直接返回
            if (!"0".equals(executeFlowResultPo.getCode())) {
                return R.error(executeFlowResultPo.getMsg());
            }
        } else {
            // 修改工单状态 2 ，15
            // 如果未匹配到流程强制修改状态，2,23
            worderInformationEntity.setWorderStatus(2);
            worderInformationEntity.setWorderStatusValue("安装中");
            worderInformationEntity.setWorderExecStatus(15);
            worderInformationEntity.setWorderExecStatusValue("安装资料待客服确认");
            worderInformationDao.updateById(worderInformationEntity);
        }

        // 记录操作日志并且，记录审核日志
        // 保存操作记录
        WorderOperationRecodeEntity worderOperationRecodeEntity = new WorderOperationRecodeEntity();
        worderOperationRecodeEntity.setUserId(user.getUserId());
        worderOperationRecodeEntity.setOperationUser(user.getEmployeeName());
        worderOperationRecodeEntity.setRecord(user.getEmployeeName() + "：比亚迪工单回退,原因：" + content);
        worderOperationRecodeEntity.setWorderNo(worderInformationEntity.getWorderNo());
        worderOperationRecodeEntity.setCreateTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
        worderOperationRecodeEntity.setWorderStatus(String.valueOf(worderInformationEntity.getWorderStatus()));
        worderOperationRecodeEntity.setWorderExecStatus(String.valueOf(worderInformationEntity.getWorderExecStatus()));
        worderOperationRecodeDao.insert(worderOperationRecodeEntity);
        // 保存审核记录
        WorderAuditResultEntity worderAuditResultEntity = new WorderAuditResultEntity();
        worderAuditResultEntity.setWorderNo(worderInformationEntity.getWorderNo());
        worderAuditResultEntity.setWorderStatus(worderInformationEntity.getWorderStatus());
        worderAuditResultEntity.setAuditUserId(user.getUserId());
        worderAuditResultEntity.setWorderAuditStatus(23);
        worderAuditResultEntity.setResult(user.getEmployeeName() + "：比亚迪工单回退,原因：" + content);
        worderAuditResultEntity.setGmtCreate(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
        worderAuditResultService.save(worderAuditResultEntity);
        return R.ok();
    }

    @Override
    public R companyCancelAuditNotice(Integer worderId) {
        // 查询工单
        LambdaQueryWrapper<WorderInformationEntity> worderInformationWrapper = Wrappers.lambdaQuery();
        worderInformationWrapper.eq(WorderInformationEntity::getWorderId, worderId);
        WorderInformationEntity worderInformationEntity = worderInformationDao.selectOne(worderInformationWrapper);
        if (worderInformationEntity == null) {
            return R.error("工单不合法");
        }
        // 调用服务商取消审核信息回传
        Result result = null;
        if (worderInformationEntity.getWorderTypeId() == 8) {
            result = businessProcessPushCheckCancelReviewInfo.callBusinessProcess(BusinessProcessPo.builder().worderId(worderInformationEntity.getWorderId()).worderNo(worderInformationEntity.getWorderNo()).auditType("1").build());
        } else {
            result = businessProcessPushCancelReviewInfo.callBusinessProcess(BusinessProcessPo.builder().worderId(worderInformationEntity.getWorderId()).worderNo(worderInformationEntity.getWorderNo()).auditType("1").build());
        }
        if (result.getCode() != 0) {
            return R.error(result.getMsg());
        }
        return R.ok();
    }

    private R getCockpitDataDetail2List(List<CockpitDataStatistics2Dto> cockpitDataStatistics2DtoList) {

        List<Map<String, Object>> cockpitDataDetailList = new ArrayList();

        // 按品牌分类数据
        Map<String, List<CockpitDataStatistics2Dto>> brandNameCockpitDataStatisticsDto = cockpitDataStatistics2DtoList.stream().collect(Collectors.groupingBy(CockpitDataStatistics2Dto::getBrandName, Collectors.toList()));

        // 按品牌统计求和并倒叙排列
        List<CockpitDataSumDto> brandNameCockpitDataSumList = getCockpitDataSum2(brandNameCockpitDataStatisticsDto);

        // 按网点分类数据
        Map<String, List<CockpitDataStatistics2Dto>> dotCockpitDataStatisticsDto = cockpitDataStatistics2DtoList.stream().collect(Collectors.groupingBy(CockpitDataStatistics2Dto::getDotName, Collectors.toList()));

        // 按网点统计求和并倒叙排列
        List<CockpitDataSumDto> dotCockpitDataSumList = getCockpitDataSum2(dotCockpitDataStatisticsDto);

        // 获取倒叙横列品牌
        LinkedHashSet<String> brandSets = brandNameCockpitDataSumList.stream().map(CockpitDataSumDto::getName).collect(Collectors.toCollection(LinkedHashSet::new));

        // 获取倒叙竖列区域
        LinkedHashSet<String> dotSets = dotCockpitDataSumList.stream().map(CockpitDataSumDto::getName).collect(Collectors.toCollection(LinkedHashSet::new));

        // 获取第一行统计数据
        Map<String, Object> brandSumStatisticsMap = getBrandSumStatisticsMap(brandSets, brandNameCockpitDataSumList);

        cockpitDataDetailList.add(brandSumStatisticsMap);
        for (String dotName : dotSets) {

            List<CockpitDataStatistics2Dto> cockpitDataStatistics2Dtos = dotCockpitDataStatisticsDto.get(dotName);

            Map<String, Object> brandStatisticsMap = initCockpitDataDetail2(brandSets);

            // 获取网点统计总数量
            brandStatisticsMap.put("sum", dotCockpitDataSumList.stream().filter(t -> dotName.equals(t.getName())).findFirst().get().getSum());
            for (CockpitDataStatistics2Dto cockpitDataStatistics2Dto : cockpitDataStatistics2Dtos) {
                brandStatisticsMap.put("dotName", cockpitDataStatistics2Dto.getDotName());
                brandStatisticsMap.put("vCode", cockpitDataStatistics2Dto.getVCode());
                brandStatisticsMap.put(cockpitDataStatistics2Dto.getBrandName(), cockpitDataStatistics2Dto.getDotNum());
            }

            cockpitDataDetailList.add(brandStatisticsMap);

        }

        return R.ok()
                .put("titile", brandSets.toArray())
                .put("data", cockpitDataDetailList);
    }


    @Override
    public R queryCockpitDetailData(Map<String, Object> params) {

        Object queryType = params.get("queryType");
        if (queryType == null || StringUtils.isBlank(queryType.toString())) {
            return R.error("queryType不能为空!");
        }
        cockpitDataHandleCondition(params);

        List<CockpitDataStatisticsDto> cockpitDataStatisticsDtoList = worderInformationDao.queryCockpitDataDetail(params);

        return getCockpitDataDetailList(cockpitDataStatisticsDtoList);
    }

    private R getCockpitDataDetailList(List<CockpitDataStatisticsDto> cockpitDataStatisticsDtoList) {

        List<Map<String, Object>> cockpitDataDetailList = new ArrayList();

        // 按品牌分类数据
        Map<String, List<CockpitDataStatisticsDto>> brandNameCockpitDataStatisticsDto = cockpitDataStatisticsDtoList.stream().collect(Collectors.groupingBy(CockpitDataStatisticsDto::getBrandName, Collectors.toList()));

        // 按品牌统计求和并倒叙排列
        List<CockpitDataSumDto> brandNameCockpitDataSumList = getCockpitDataSum(brandNameCockpitDataStatisticsDto);

        // 按区域分类数据
        Map<String, List<CockpitDataStatisticsDto>> provinceCockpitDataStatisticsDto = cockpitDataStatisticsDtoList.stream().collect(Collectors.groupingBy(CockpitDataStatisticsDto::getProvinceName, Collectors.toList()));

        // 按区域统计求和并倒叙排列
        List<CockpitDataSumDto> provinceCockpitDataSumList = getCockpitDataSum(provinceCockpitDataStatisticsDto);

        // 获取倒叙横列品牌
        LinkedHashSet<String> brandSets = brandNameCockpitDataSumList.stream().map(CockpitDataSumDto::getName).collect(Collectors.toCollection(LinkedHashSet::new));

        // 获取倒叙竖列区域
        LinkedHashSet<String> provinceSets = provinceCockpitDataSumList.stream().map(CockpitDataSumDto::getName).collect(Collectors.toCollection(LinkedHashSet::new));

        // 获取第一行统计数据
        Map<String, Object> brandSumStatisticsMap = getBrandSumStatisticsMap(brandSets, brandNameCockpitDataSumList);

        cockpitDataDetailList.add(brandSumStatisticsMap);
        for (String province : provinceSets) {

            List<CockpitDataStatisticsDto> cockpitDataStatisticsDtos = provinceCockpitDataStatisticsDto.get(province);

            Map<String, Object> brandStatisticsMap = initCockpitDataDetail(brandSets);

            // 获取区域统计总数量
            brandStatisticsMap.put("sum", provinceCockpitDataSumList.stream().filter(t -> province.equals(t.getName())).findFirst().get().getSum());
            for (CockpitDataStatisticsDto cockpitDataStatisticsDto : cockpitDataStatisticsDtos) {
                brandStatisticsMap.put("province", cockpitDataStatisticsDto.getProvinceName());
                brandStatisticsMap.put(cockpitDataStatisticsDto.getBrandName(), cockpitDataStatisticsDto.getProvinceNum());
            }

            cockpitDataDetailList.add(brandStatisticsMap);

        }

        return R.ok()
                .put("titile", brandSets.toArray())
                .put("data", cockpitDataDetailList);
    }

    /**
     * 获取第一行统计数据
     *
     * @param brandSets
     * @param brandNameCockpitDataSumList
     * @return
     */
    private Map<String, Object> getBrandSumStatisticsMap(LinkedHashSet<String> brandSets, List<CockpitDataSumDto> brandNameCockpitDataSumList) {
        Map<String, Object> brandStatisticsMap = new HashMap<>();
        brandStatisticsMap.put("sum", brandNameCockpitDataSumList.stream().mapToInt(CockpitDataSumDto::getSum).sum());
        brandStatisticsMap.put("province", "合计");
        brandStatisticsMap.put("dotName", "合计");
        brandStatisticsMap.put("vCode", "合计");
        for (CockpitDataSumDto cockpitDataSumDto : brandNameCockpitDataSumList) {
            brandStatisticsMap.put(cockpitDataSumDto.getName(), cockpitDataSumDto.getSum());
        }
        return brandStatisticsMap;
    }

    /**
     * 按名称求和并倒叙排列
     *
     * @param cockpitDataStatisticsMap
     * @return
     */
    private List<CockpitDataSumDto> getCockpitDataSum(Map<String, List<CockpitDataStatisticsDto>> cockpitDataStatisticsMap) {

        List<CockpitDataSumDto> cockpitDataSumDtoList = new ArrayList<>();
        for (Map.Entry<String, List<CockpitDataStatisticsDto>> entry : cockpitDataStatisticsMap.entrySet()) {
            CockpitDataSumDto sumDto = new CockpitDataSumDto();
            sumDto.setName(entry.getKey());
            sumDto.setSum(entry.getValue().stream().mapToInt(CockpitDataStatisticsDto::getProvinceNum).sum());
            cockpitDataSumDtoList.add(sumDto);
        }
        return cockpitDataSumDtoList.stream().sorted(Comparator.comparing(CockpitDataSumDto::getSum, Comparator.reverseOrder())).collect(Collectors.toList());
    }

    /**
     * 按名称求和并倒叙排列
     *
     * @param cockpitDataStatistics2Map
     * @return
     */
    private List<CockpitDataSumDto> getCockpitDataSum2(Map<String, List<CockpitDataStatistics2Dto>> cockpitDataStatistics2Map) {

        List<CockpitDataSumDto> cockpitDataSumDtoList = new ArrayList<>();
        for (Map.Entry<String, List<CockpitDataStatistics2Dto>> entry : cockpitDataStatistics2Map.entrySet()) {
            CockpitDataSumDto sumDto = new CockpitDataSumDto();
            sumDto.setName(entry.getKey());
            sumDto.setSum(entry.getValue().stream().mapToInt(CockpitDataStatistics2Dto::getDotNum).sum());
            cockpitDataSumDtoList.add(sumDto);
        }
        return cockpitDataSumDtoList.stream().sorted(Comparator.comparing(CockpitDataSumDto::getSum, Comparator.reverseOrder())).collect(Collectors.toList());
    }

    /**
     * 初始化统计表数据对象
     *
     * @param brandSets
     * @return
     */
    private Map<String, Object> initCockpitDataDetail(LinkedHashSet<String> brandSets) {
        Map<String, Object> cockpitDataDetailMap = new HashMap<>();
        cockpitDataDetailMap.put("province", "");
        cockpitDataDetailMap.put("sum", "");
        for (String brand : brandSets) {
            cockpitDataDetailMap.put(brand, 0);
        }
        return cockpitDataDetailMap;
    }

    /**
     * 初始化统计表数据对象
     *
     * @param brandSets
     * @return
     */
    private Map<String, Object> initCockpitDataDetail2(LinkedHashSet<String> brandSets) {
        Map<String, Object> cockpitDataDetailMap = new HashMap<>();
        cockpitDataDetailMap.put("dotName", "");
        cockpitDataDetailMap.put("sum", "");
        for (String brand : brandSets) {
            cockpitDataDetailMap.put(brand, 0);
        }
        return cockpitDataDetailMap;
    }

    /**
     * 过滤自身品牌与区域
     *
     * @param userId
     * @param cityIdList
     * @param areaIdList
     * @param brandList
     */
    private void setUserBrandAndArea(Long userId, List<Long> cityIdList, List<Long> areaIdList, List<Integer> brandList) {
        List<MagrAreaBrand> areaBrand = sysUserService.listUserBrandRegion(String.valueOf(userId));
        Set<Integer> regionSet = new HashSet<>();
        Set<Integer> brandSet = new HashSet<>();
        areaBrand.stream().filter(item -> item != null).map(item -> {
            if (item.getAreaId() != null) {
                regionSet.add(item.getAreaId());
            }
            if (item.getBrandId() != null) {
                brandSet.add(item.getBrandId());
            }
            return null;
        }).collect(Collectors.toList());
        List<Integer> reginList = new ArrayList<>(regionSet);

        for (Integer brandId : brandSet) {
            brandList.add(brandId);
        }

        setUserAreaId(cityIdList, areaIdList, reginList);
    }

    private void setUserAreaId(List<Long> cityIdList, List<Long> areaIdList, List<Integer> reginList) {
        for (int i = 0; i < reginList.size(); i++) {
            QueryWrapper<BizRegionEntity> query = new QueryWrapper<>();
            String id = reginList.get(i).toString();
            query.eq("id", id);
            BizRegionEntity bizRegionEntity = bizRegionDao.selectOne(query);
            if (bizRegionEntity.getType() == 1) {
                //省
                cityIdList.add(bizRegionEntity.getId());
            } else if (bizRegionEntity.getType() == 2) {
                //市
                QueryWrapper<BizRegionEntity> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("pid", bizRegionEntity.getId());
                List<BizRegionEntity> bizRegionEntities = bizRegionDao.selectList(queryWrapper);
                for (int j = 0; j < bizRegionEntities.size(); j++) {
                    areaIdList.add(bizRegionEntities.get(j).getId());
                }
            } else if (bizRegionEntity.getType() == 3) {
                //县
                areaIdList.add(bizRegionEntity.getId());
            }
        }
    }

    /**
     * 封装运营工单看板数据
     *
     * @param cockpitDataMap 运营工单数据
     * @param permissions    看板权限
     * @return
     */
    private List<Map<String, Object>> getCockpitDataList(Map<String, Object> cockpitDataMap,
                                                         Map<String, Object> cockpitCSDataMap,
                                                         Map<String, Object> cockpitJRDataMap,
                                                         Map<String, Object> cockpitMRDataMap,
                                                         Map<String, Object> cockpitHRDataMap,
                                                         Map<String, Boolean> permissions, Map<String, Object> params) {
        boolean b = params.get("cockpitList") == null;
        List<Map<String, Object>> list = new ArrayList<>();
        if (b) {
            list.add(setCockpitData(2, "分配中",
                    2, "-", "未派单",
                    "fp_wpd", cockpitDataMap,
                    "fp_cs_wpd", cockpitCSDataMap,
                    "fp_jr_wpd", cockpitJRDataMap,
                    "fp_mr_wpd", cockpitMRDataMap,
                    "fp_hr_wpd", cockpitHRDataMap,
                    permissions.get("hasFpWpd")));

            list.add(setCockpitData(0, "分配中",
                    0, "-", "服务经理未派单",
                    "fp_fwjlwpd", cockpitDataMap,
                    "fp_cs_fwjlwpd", cockpitCSDataMap,
                    "fp_jr_fwjlwpd", cockpitJRDataMap,
                    "fp_mr_fwjlwpd", cockpitMRDataMap,
                    "fp_hr_fwjlwpd", cockpitHRDataMap,
                    permissions.get("hasFpFwjlwpd")));

            list.add(setCockpitData(6, "服务中",
                    3, "勘测中", "网点已接单",
                    "kc_wdyjd", cockpitDataMap,
                    "kc_cs_wdyjd", cockpitCSDataMap,
                    "kc_jr_wdyjd", cockpitJRDataMap,
                    "kc_mr_wdyjd", cockpitMRDataMap,
                    "kc_hr_wdyjd", cockpitHRDataMap,
                    permissions.get("hasKcWdyjd")));

            list.add(setCockpitData(0, "服务中",
                    0, "勘测中", "待勘测预约",
                    "kc_dkcyy", cockpitDataMap,
                    "kc_cs_dkcyy", cockpitCSDataMap,
                    "kc_jr_dkcyy", cockpitJRDataMap,
                    "kc_mr_dkcyy", cockpitMRDataMap,
                    "kc_hr_dkcyy", cockpitHRDataMap,
                    permissions.get("hasKcDkcyy")));

            list.add(setCockpitData(0, "服务中",
                    0, "勘测中", "待勘测",
                    "kc_dkc", cockpitDataMap,
                    "kc_cs_dkc", cockpitCSDataMap,
                    "kc_jr_dkc", cockpitJRDataMap,
                    "kc_mr_dkc", cockpitMRDataMap,
                    "kc_hr_dkc", cockpitHRDataMap,
                    permissions.get("hasKcDkc")));


            list.add(setCockpitData(0, "服务中",
                    3, "安装中", "等待充电桩及备件",
                    "az_ddcdzjpj", cockpitDataMap,
                    "az_cs_ddcdzjpj", cockpitCSDataMap,
                    "az_jr_ddcdzjpj", cockpitJRDataMap,
                    "az_mr_ddcdzjpj", cockpitMRDataMap,
                    "az_hr_ddcdzjpj", cockpitHRDataMap,
                    permissions.get("hasAzDdcdzjpj")));

            list.add(setCockpitData(0, "服务中",
                    0, "安装中", "待安装预约",
                    "az_dazyy", cockpitDataMap,
                    "az_cs_dazyy", cockpitCSDataMap,
                    "az_jr_dazyy", cockpitJRDataMap,
                    "az_mr_dazyy", cockpitMRDataMap,
                    "az_hr_dazyy", cockpitHRDataMap,
                    permissions.get("hasAzDazyy")));

            list.add(setCockpitData(0, "服务中",
                    0, "安装中", "待安装",
                    "az_daz", cockpitDataMap,
                    "az_cs_daz", cockpitCSDataMap,
                    "az_jr_daz", cockpitJRDataMap,
                    "az_mr_daz", cockpitMRDataMap,
                    "az_hr_daz", cockpitHRDataMap,
                    permissions.get("hasAzDaz")));


            list.add(setCockpitData(8, "资料提交审核",
                    4, "勘测资料", "勘测资料未提交",
                    "kczl_kczlwtj", cockpitDataMap,
                    "kczl_cs_kczlwtj", cockpitCSDataMap,
                    "kczl_jr_kczlwtj", cockpitJRDataMap,
                    "kczl_mr_kczlwtj", cockpitMRDataMap,
                    "kczl_hr_kczlwtj", cockpitHRDataMap,
                    permissions.get("hasKczlKczlwtj")));


            list.add(setCockpitData(0, "资料提交审核",
                    0, "勘测资料", "勘测资料提交待审核",
                    "kczl_kczltjdsh", cockpitDataMap,
                    "kczl_cs_kczltjdsh", cockpitCSDataMap,
                    "kczl_jr_kczltjdsh", cockpitJRDataMap,
                    "kczl_mr_kczltjdsh", cockpitMRDataMap,
                    "kczl_hr_kczltjdsh", cockpitHRDataMap,
                    permissions.get("hasKczlKczltjdsh")));

            list.add(setCockpitData(0, "资料提交审核",
                    0, "勘测资料", "勘测资料整改中",
                    "kczl_kczlzgz", cockpitDataMap,
                    "kczl_cs_kczlzgz", cockpitCSDataMap,
                    "kczl_jr_kczlzgz", cockpitJRDataMap,
                    "kczl_mr_kczlzgz", cockpitMRDataMap,
                    "kczl_hr_kczlzgz", cockpitHRDataMap,
                    permissions.get("hasKczlKczlzgz")));

            list.add(setCockpitData(0, "资料提交审核",
                    0, "勘测资料", "待客服确认",
                    "kczl_dkfqr", cockpitDataMap,
                    "kczl_cs_dkfqr", cockpitCSDataMap,
                    "kczl_jr_dkfqr", cockpitJRDataMap,
                    "kczl_mr_dkfqr", cockpitMRDataMap,
                    "kczl_hr_dkfqr", cockpitHRDataMap,
                    permissions.get("hasKczlDkfqr")));


            list.add(setCockpitData(0, "资料提交审核",
                    4, "安装资料", "安装资料未提交",
                    "azzl_azzlwtj", cockpitDataMap,
                    "azzl_cs_azzlwtj", cockpitCSDataMap,
                    "azzl_jr_azzlwtj", cockpitJRDataMap,
                    "azzl_mr_azzlwtj", cockpitMRDataMap,
                    "azzl_hr_azzlwtj", cockpitHRDataMap,
                    permissions.get("hasAzzlAzzlwtj")));

            list.add(setCockpitData(0, "资料提交审核",
                    0, "安装资料", "安装资料已提交待审核",
                    "azzl_azzlytjdsh", cockpitDataMap,
                    "azzl_cs_azzlytjdsh", cockpitCSDataMap,
                    "azzl_jr_azzlytjdsh", cockpitJRDataMap,
                    "azzl_mr_azzlytjdsh", cockpitMRDataMap,
                    "azzl_hr_azzlytjdsh", cockpitHRDataMap,
                    permissions.get("hasAzzlAzzlytjdsh")));

            list.add(setCockpitData(0, "资料提交审核",
                    0, "安装资料", "安装资料整改中",
                    "azzl_azzlzgz", cockpitDataMap,
                    "azzl_cs_azzlzgz", cockpitCSDataMap,
                    "azzl_jr_azzlzgz", cockpitJRDataMap,
                    "azzl_mr_azzlzgz", cockpitMRDataMap,
                    "azzl_hr_azzlzgz", cockpitHRDataMap,
                    permissions.get("hasAzzlAzzlzgz")));

            list.add(setCockpitData(0, "资料提交审核",
                    0, "安装资料", "安装资料待客服审核",
                    "azzl_azzldkfsh", cockpitDataMap,
                    "azzl_cs_azzldkfsh", cockpitCSDataMap,
                    "azzl_jr_azzldkfsh", cockpitJRDataMap,
                    "azzl_mr_azzldkfsh", cockpitMRDataMap,
                    "azzl_hr_azzldkfsh", cockpitHRDataMap,
                    permissions.get("hasAzzlAzzldkfsh")));
        } else {
            list.add(setCockpitData(2, "分配中",
                    2, "-", "未派单",
                    "fp_wpd", cockpitDataMap,
                    "fp_cs_wpd", cockpitCSDataMap,
                    "fp_jr_wpd", cockpitJRDataMap,
                    "fp_mr_wpd", cockpitMRDataMap,
                    "fp_hr_wpd", cockpitHRDataMap,
                    permissions.get("hasFpWpd")));

            list.add(setCockpitData(0, "分配中",
                    0, "-", "服务经理未派单",
                    "fp_fwjlwpd", cockpitDataMap,
                    "fp_cs_fwjlwpd", cockpitCSDataMap,
                    "fp_jr_fwjlwpd", cockpitJRDataMap,
                    "fp_mr_fwjlwpd", cockpitMRDataMap,
                    "fp_hr_fwjlwpd", cockpitHRDataMap,
                    permissions.get("hasFpFwjlwpd")));

            list.add(setCockpitData(5, "服务中",
                    3, "勘测中", "网点已接单",
                    "kc_wdyjd", cockpitDataMap,
                    "kc_cs_wdyjd", cockpitCSDataMap,
                    "kc_jr_wdyjd", cockpitJRDataMap,
                    "kc_mr_wdyjd", cockpitMRDataMap,
                    "kc_hr_wdyjd", cockpitHRDataMap,
                    permissions.get("hasKcWdyjd")));

            list.add(setCockpitData(0, "服务中",
                    0, "勘测中", "待勘测预约",
                    "kc_dkcyy", cockpitDataMap,
                    "kc_cs_dkcyy", cockpitCSDataMap,
                    "kc_jr_dkcyy", cockpitJRDataMap,
                    "kc_mr_dkcyy", cockpitMRDataMap,
                    "kc_hr_dkcyy", cockpitHRDataMap,
                    permissions.get("hasKcDkcyy")));

            list.add(setCockpitData(0, "服务中",
                    0, "勘测中", "待勘测",
                    "kc_dkc", cockpitDataMap,
                    "kc_cs_dkc", cockpitCSDataMap,
                    "kc_jr_dkc", cockpitJRDataMap,
                    "kc_mr_dkc", cockpitMRDataMap,
                    "kc_hr_dkc", cockpitHRDataMap,
                    permissions.get("hasKcDkc")));


//            list.add(setCockpitData(0, "服务中",
//                    3, "安装中", "等待充电桩及备件",
//                    "az_ddcdzjpj", cockpitDataMap,
//                    "az_cs_ddcdzjpj", cockpitCSDataMap,
//                    "az_jr_ddcdzjpj", cockpitJRDataMap,
//                    "az_mr_ddcdzjpj", cockpitMRDataMap,
//                    "az_hr_ddcdzjpj", cockpitHRDataMap,
//                    permissions.get("hasAzDdcdzjpj")));

            list.add(setCockpitData(0, "服务中",
                    2, "安装中", "待安装预约",
                    "az_dazyy", cockpitDataMap,
                    "az_cs_dazyy", cockpitCSDataMap,
                    "az_jr_dazyy", cockpitJRDataMap,
                    "az_mr_dazyy", cockpitMRDataMap,
                    "az_hr_dazyy", cockpitHRDataMap,
                    permissions.get("hasAzDazyy")));

            list.add(setCockpitData(0, "服务中",
                    0, "安装中", "待安装",
                    "az_daz", cockpitDataMap,
                    "az_cs_daz", cockpitCSDataMap,
                    "az_jr_daz", cockpitJRDataMap,
                    "az_mr_daz", cockpitMRDataMap,
                    "az_hr_daz", cockpitHRDataMap,
                    permissions.get("hasAzDaz")));


            list.add(setCockpitData(8, "资料提交审核",
                    4, "勘测资料", "勘测资料未提交",
                    "kczl_kczlwtj", cockpitDataMap,
                    "kczl_cs_kczlwtj", cockpitCSDataMap,
                    "kczl_jr_kczlwtj", cockpitJRDataMap,
                    "kczl_mr_kczlwtj", cockpitMRDataMap,
                    "kczl_hr_kczlwtj", cockpitHRDataMap,
                    permissions.get("hasKczlKczlwtj")));


            list.add(setCockpitData(0, "资料提交审核",
                    0, "勘测资料", "勘测资料提交待审核",
                    "kczl_kczltjdsh", cockpitDataMap,
                    "kczl_cs_kczltjdsh", cockpitCSDataMap,
                    "kczl_jr_kczltjdsh", cockpitJRDataMap,
                    "kczl_mr_kczltjdsh", cockpitMRDataMap,
                    "kczl_hr_kczltjdsh", cockpitHRDataMap,
                    permissions.get("hasKczlKczltjdsh")));

            list.add(setCockpitData(0, "资料提交审核",
                    0, "勘测资料", "勘测资料整改中",
                    "kczl_kczlzgz", cockpitDataMap,
                    "kczl_cs_kczlzgz", cockpitCSDataMap,
                    "kczl_jr_kczlzgz", cockpitJRDataMap,
                    "kczl_mr_kczlzgz", cockpitMRDataMap,
                    "kczl_hr_kczlzgz", cockpitHRDataMap,
                    permissions.get("hasKczlKczlzgz")));

            list.add(setCockpitData(0, "资料提交审核",
                    0, "勘测资料", "待客服确认",
                    "kczl_dkfqr", cockpitDataMap,
                    "kczl_cs_dkfqr", cockpitCSDataMap,
                    "kczl_jr_dkfqr", cockpitJRDataMap,
                    "kczl_mr_dkfqr", cockpitMRDataMap,
                    "kczl_hr_dkfqr", cockpitHRDataMap,
                    permissions.get("hasKczlDkfqr")));


            list.add(setCockpitData(0, "资料提交审核",
                    4, "安装资料", "安装资料未提交",
                    "azzl_azzlwtj", cockpitDataMap,
                    "azzl_cs_azzlwtj", cockpitCSDataMap,
                    "azzl_jr_azzlwtj", cockpitJRDataMap,
                    "azzl_mr_azzlwtj", cockpitMRDataMap,
                    "azzl_hr_azzlwtj", cockpitHRDataMap,
                    permissions.get("hasAzzlAzzlwtj")));

            list.add(setCockpitData(0, "资料提交审核",
                    0, "安装资料", "安装资料已提交待审核",
                    "azzl_azzlytjdsh", cockpitDataMap,
                    "azzl_cs_azzlytjdsh", cockpitCSDataMap,
                    "azzl_jr_azzlytjdsh", cockpitJRDataMap,
                    "azzl_mr_azzlytjdsh", cockpitMRDataMap,
                    "azzl_hr_azzlytjdsh", cockpitHRDataMap,
                    permissions.get("hasAzzlAzzlytjdsh")));

            list.add(setCockpitData(0, "资料提交审核",
                    0, "安装资料", "安装资料整改中",
                    "azzl_azzlzgz", cockpitDataMap,
                    "azzl_cs_azzlzgz", cockpitCSDataMap,
                    "azzl_jr_azzlzgz", cockpitJRDataMap,
                    "azzl_mr_azzlzgz", cockpitMRDataMap,
                    "azzl_hr_azzlzgz", cockpitHRDataMap,
                    permissions.get("hasAzzlAzzlzgz")));

            list.add(setCockpitData(0, "资料提交审核",
                    0, "安装资料", "安装资料待客服审核",
                    "azzl_azzldkfsh", cockpitDataMap,
                    "azzl_cs_azzldkfsh", cockpitCSDataMap,
                    "azzl_jr_azzldkfsh", cockpitJRDataMap,
                    "azzl_mr_azzldkfsh", cockpitMRDataMap,
                    "azzl_hr_azzldkfsh", cockpitHRDataMap,
                    permissions.get("hasAzzlAzzldkfsh")));

        }

//        list.add(setCockpitData(0, "资料提交审核",
//                0, "安装资料", "安装资料无误待上传车企",
//                "azzl_azzlwwdsccq", cockpitDataMap,
//                "azzl_cs_azzlwwdsccq", cockpitCSDataMap,
//                "azzl_jr_azzlwwdsccq", cockpitJRDataMap,
//                "azzl_mr_azzlwwdsccq", cockpitMRDataMap,
//                "azzl_hr_azzlwwdsccq", cockpitHRDataMap,
//                permissions.get("hasAzzlAzzlwwdsccq")));
        return list;
    }

    /**
     * 校验权限
     *
     * @param userRoleIdList           用户角色id
     * @param listPermissionDictionary 字典配置权限
     * @param permissionsMap           权限保存集合
     */
    private void verifyPermissions(List<Long> userRoleIdList, List<SysDictionaryDetailEntity> listPermissionDictionary, Map<String, Boolean> permissionsMap) {
        Map<String, List<SysDictionaryDetailEntity>> permissionDictionaryGroup = listPermissionDictionary.stream().collect(Collectors.groupingBy(SysDictionaryDetailEntity::getDetailNumber, Collectors.toList()));

        if (userRoleIdList != null) {
            for (Long roleId : userRoleIdList) {
                List<SysDictionaryDetailEntity> listRoleIdPermissionDictionary = permissionDictionaryGroup.get(roleId.toString());
                if (listRoleIdPermissionDictionary != null) {
                    for (SysDictionaryDetailEntity dictionaryDetailEntity : listRoleIdPermissionDictionary) {
                        setPermissions(dictionaryDetailEntity.getDetailName(), permissionsMap);
                    }
                }
            }
        }
    }

    private void setPermissions(String permissionsDetail, Map<String, Boolean> permissionsMap) {
        String[] permissions = permissionsDetail.split(",");
        for (String permission : permissions) {
            setPermission(permission, permissionsMap);
        }
    }

    private void setPermission(String permission, Map<String, Boolean> permissionsMap) {
        if ("未派单".equals(permission)) {
            permissionsMap.put("hasFpWpd", true);
        } else if ("服务经理未派单".equals(permission)) {
            permissionsMap.put("hasFpFwjlwpd", true);
        } else if ("网点已接单".equals(permission)) {
            permissionsMap.put("hasKcWdyjd", true);
        } else if ("待勘测预约".equals(permission)) {
            permissionsMap.put("hasKcDkcyy", true);
        } else if ("待勘测".equals(permission)) {
            permissionsMap.put("hasKcDkc", true);
        } else if ("等待充电桩及备件".equals(permission)) {
            permissionsMap.put("hasAzDdcdzjpj", true);
        } else if ("待安装预约".equals(permission)) {
            permissionsMap.put("hasAzDazyy", true);
        } else if ("待安装".equals(permission)) {
            permissionsMap.put("hasAzDaz", true);
        } else if ("勘测资料未提交".equals(permission)) {
            permissionsMap.put("hasKczlKczlwtj", true);
        } else if ("勘测资料提交待审核".equals(permission)) {
            permissionsMap.put("hasKczlKczltjdsh", true);
        } else if ("勘测资料整改中".equals(permission)) {
            permissionsMap.put("hasKczlKczlzgz", true);
        } else if ("待客服确认".equals(permission)) {
            permissionsMap.put("hasKczlDkfqr", true);
        } else if ("安装资料未提交".equals(permission)) {
            permissionsMap.put("hasAzzlAzzlwtj", true);
        } else if ("安装资料已提交待审核".equals(permission)) {
            permissionsMap.put("hasAzzlAzzlytjdsh", true);
        } else if ("安装资料整改中".equals(permission)) {
            permissionsMap.put("hasAzzlAzzlzgz", true);
        } else if ("安装资料待客服审核".equals(permission)) {
            permissionsMap.put("hasAzzlAzzldkfsh", true);
        } else if ("安装资料无误待上传车企".equals(permission)) {
            permissionsMap.put("hasAzzlAzzlwwdsccq", true);
        }
    }

    /**
     * 标准化响应数据
     *
     * @param type1Num         工单类型一数量
     * @param type1            工单类型一
     * @param type2Num         工单类型二数量
     * @param type2            工单类型二
     * @param type3            工单类型三
     * @param queryType        查询类型
     * @param cockpitDataMap   原始数据
     * @param csType           超时数据类型
     * @param cockpitCsDataMap 超时数据
     * @param jrType           今日工单数据类型
     * @param cockpitJrDataMap 今日工单
     * @param mrType           明日工单数据类型
     * @param cockpitMrDataMap 明日工单
     * @param hasPermission    权限集合
     * @return
     */
    private Map<String, Object> setCockpitData(Integer type1Num, String type1,
                                               Integer type2Num, String type2,
                                               String type3, String queryType,
                                               Map<String, Object> cockpitDataMap,
                                               String csType, Map<String, Object> cockpitCsDataMap,
                                               String jrType, Map<String, Object> cockpitJrDataMap,
                                               String mrType, Map<String, Object> cockpitMrDataMap,
                                               String hrType, Map<String, Object> cockpitHrDataMap,
                                               Boolean hasPermission) {
        Map<String, Object> map = new HashMap<>();

        map.put("type1Num", type1Num);
        map.put("type1", type1);
        map.put("type2Num", type2Num);
        map.put("type2", type2);
        map.put("type3", type3);
        map.put("queryType", queryType);
        map.put("csType", csType);
        map.put("jrType", jrType);
        map.put("mrType", mrType);
        map.put("hrType", hrType);

        Object csValue = 0;
        Object jrValue = 0;
        Object mrValue = 0;
        Object hrValue = 0;
        if (cockpitCsDataMap != null) {
            csValue = cockpitCsDataMap.get(csType);
        }
        if (cockpitJrDataMap != null) {
            jrValue = cockpitJrDataMap.get(jrType);
        }

        if (cockpitMrDataMap != null) {
            mrValue = cockpitMrDataMap.get(mrType);
        }
        if (cockpitHrDataMap != null) {
            hrValue = cockpitHrDataMap.get(hrType);
        }

        if (hasPermission && cockpitDataMap != null && cockpitDataMap.containsKey(queryType)) {
            map.put("worderNum", cockpitDataMap.get(queryType));
            if (cockpitDataMap != null) {
                map.put("worderNum", cockpitDataMap.get(queryType));
            } else {
                map.put("worderNum", 0);
            }

            setWorderNum(map, "worderCsNum", csValue);

            setWorderNum(map, "worderJrNum", jrValue);

            setWorderNum(map, "worderMrNum", mrValue);

            setWorderNum(map, "worderHrNum", hrValue);

        } else {
            map.put("worderNum", 0);

            setWorderNumNoPermission(map, "worderCsNum", csValue);

            setWorderNumNoPermission(map, "worderJrNum", jrValue);

            setWorderNumNoPermission(map, "worderMrNum", mrValue);

            setWorderNumNoPermission(map, "worderHrNum", hrValue);
        }
        return map;
    }

    private void setWorderNum(Map<String, Object> map, String key, Object value) {
        if (value == null) {
            map.put(key, "-");
        } else {
            map.put(key, value);
        }
    }

    private void setWorderNumNoPermission(Map<String, Object> map, String key, Object value) {
        if (value == null) {
            map.put(key, "-");
        } else {
            map.put(key, 0);
        }
    }


    @Override
    public List<Integer> getStoreIdByWorderNo(String woderNo, List<String> outSn) {
        return worderInformationDao.getStoreIdByWorderNo(woderNo, outSn);
    }

    @Override
    public String getStoreNameById(Integer storeId) {
        return worderInformationDao.getStoreNameById(storeId);
    }

    @Override
    public R getNoInstallReasonList() {
        List<NoInstallReasonVo> noInstallReasonVoList = worderInformationDao.getNoInstallReasonList();
        return R.ok().put("NoInstallReason", noInstallReasonVoList);
    }

    @Override
    public R getWorderLevelList() {
        List<NoInstallReasonVo> noInstallReasonVoList = worderInformationDao.getWorderLevelList();
        return R.ok().put("NoInstallReason", noInstallReasonVoList);
    }

    @Override
    public Boolean checkTransferOrder(String worderNo) {
        if (StringUtils.isBlank(worderNo)) {
            return false;
        }
        // 查询是否存在订单属性信息
        WorderInformationAttributeEntity worderInformationAttributeEntity = worderInformationAttributeDao.selectAttributeByWorderNo(worderNo, "Transfer-Order", "TransferOrder");
        if (worderInformationAttributeEntity == null) {
            return false;
        }
        return "1".equals(worderInformationAttributeEntity.getAttributeValue());
    }

    @Override
    @Transactional
    public String updateMaterial(String worderNo, Integer type) {
        try {
            if (com.youngking.lenmoncore.common.utils.StringUtils.isBlank(worderNo)) {
                return "工单号不为空";
            }
            SysUserEntity user = getUser();
            Long userId = user.getUserId();
            Map<String, Object> params = new HashMap<>();
            params.put("page", 1);
            params.put("worderNo", worderNo);
            params.put("limit", 10);
            PageUtils pageUtils = this.queryAll(params);
            List<?> list = pageUtils.getList();
            if (list.isEmpty()) {
                return "没有权限操作该工单";
            }
            WorderInfoEntity entity = (WorderInfoEntity) list.get(0);
            if (entity.getWorderStatus() != 2) {
                return "只有安装中的工单才能修改物料";
            }

            //TODO 检查负激励
            baseMapper.updateMaterial(worderNo);
            String msg = "回退修改物料";
            //如果是重新提交将状态修改
            if (2 == type) {
                msg = "重新提交资料";
                WorderInfoEntity w1 = new WorderInfoEntity();
                w1.setWorderId(entity.getWorderId());
                w1.setWorderStatus(2);
                w1.setWorderStatusValue("安装中");
                w1.setWorderExecStatus(12);
                w1.setWorderExecStatusValue("安装资料未提交");
                baseMapper.updateWorderInfo(w1);
                FlowWorderEntity f1 = new FlowWorderEntity();
                f1.setWorderId(entity.getWorderId());
                f1.setNextFlowChildCode("anzhuangziliaoweitijiao");
                QueryWrapper<FlowWorderEntity> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("worder_id", entity.getWorderId());
                flowWorderDao.update(f1, queryWrapper);
            }
            saveWorderRemark(worderNo, worderNo + msg, user.getUsername() + msg, userId.intValue());

            OperationRecord work = new OperationRecord(user.getUsername(), user.getUsername() + msg, entity.getDotId().longValue(), user.getUsername(), 5, worderNo, entity.getWorderStatus().toString(), entity.getWorderExecStatus().toString());
            Date currentDate = new Date();
            long time = currentDate.getTime();
            work.setCreateTime(new Date(time + 2000));
            workMsgDao.insertOperation(work);
        } catch (Exception e) {
            log.error("回退修改物料发生异常 工单号{}", worderNo, e);
            throw new RuntimeException(e);
        }
        return null;
    }

    @Override
    public WorderInfoDTO findWorderInfo(String worderNo) {
        return worderInformationDao.findWorderInfo(worderNo);
    }

    /**
     * push_dataCheck
     * @return
     */
    @Override
    public List<Integer> selectCaWorderInfo() {
        return worderInformationDao.selectCaWorderInfo();
    }

    @Override
    public WorderInfoEntity getWorderInfoEntityByWorderId(Integer worderId) {
        return baseMapper.getByWorderId(worderId);
    }

    @Override
    public WorderInformationEntity getDotInfoByWorderNo(String worderNo) {
        return baseMapper.getDotInfoByWorderNo(worderNo);
    }

    @Override
    @Transactional
    public String updateWorderExecStatus(String worderNo, int worderStatus, int worderExecStatus, String nextFlowChildCode) {
        try {
            if (com.youngking.lenmoncore.common.utils.StringUtils.isBlank(worderNo)) {
                return "工单号不为空";
            }
            SysUserEntity user = getUser();
            Long userId = user.getUserId();
            Map<String, Object> params = new HashMap<>();
            params.put("page", 1);
            params.put("worderNo", worderNo);
            params.put("limit", 10);
            PageUtils pageUtils = this.queryAll(params);
            List<?> list = pageUtils.getList();
            if (list.isEmpty()) {
                return "没有权限操作该工单";
            }
            WorderInfoEntity entity = (WorderInfoEntity) list.get(0);
            if (entity.getWorderStatus() != 2) {
                return "只有安装中的工单才能回退至待勘测";
            }
            baseMapper.updateMaterial(worderNo);
            String msg = "回退至待勘测";

            WorderInfoEntity w1 = new WorderInfoEntity();
            w1.setWorderId(entity.getWorderId());
            w1.setWorderStatus(worderStatus);
            w1.setWorderStatusValue("勘测中");
            w1.setWorderExecStatus(worderExecStatus);
            w1.setWorderExecStatusValue("勘测资料未提交");
            if (worderStatus == 1) {
                w1.setInstallAppointTime("aaaaaa");
            }
            baseMapper.updateWorderInfo(w1);
            FlowWorderEntity f1 = new FlowWorderEntity();
            f1.setWorderId(entity.getWorderId());
            f1.setNextFlowChildCode(nextFlowChildCode);
            QueryWrapper<FlowWorderEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("worder_id", entity.getWorderId());
            flowWorderDao.update(f1, queryWrapper);

            saveWorderRemark(worderNo, worderNo + msg, user.getUsername() + msg, userId.intValue());

            OperationRecord work = new OperationRecord(user.getUsername(), user.getUsername() + msg, entity.getDotId().longValue(), user.getUsername(), 5, worderNo, entity.getWorderStatus().toString(), entity.getWorderExecStatus().toString());
            Date currentDate = new Date();
            long time = currentDate.getTime();
            work.setCreateTime(new Date(time + 2000));
            workMsgDao.insertOperation(work);
        } catch (Exception e) {
            log.error("回退至待勘测发生异常 工单号{}", worderNo, e);
            throw new RuntimeException(e);
        }
        return null;
    }

    @Override
    public R proDotCockpitData(BydBaobiaoParam param) {
        if (CollectionUtils.isEmpty(param.getProvinceIds())) {
            return R.error("请选择省份");
        }
        if (CollectionUtils.isEmpty(param.getBrandIds())) {
            List<Integer> brandIds = new ArrayList<>();
            brandIds.add(21);
            brandIds.add(22);
            brandIds.add(87);
            brandIds.add(88);
            brandIds.add(89);
            brandIds.add(90);
            brandIds.add(91);
            brandIds.add(121);
            param.setBrandIds(brandIds);
        }
        List<BizRegionEntity> bizRegionEntities = null;
        //安装时效列表
        List<BydBaoBiaoResult> pjazsxList = null;
        //安装及时率
        List<BydBaoBiaoResult> azjslList = null;

        //安装率
        List<BydBaoBiaoResult> azlList = null;

        CompletableFuture<List<BydBaoBiaoResult>> f1 = CompletableFuture.supplyAsync(() -> {
            return baseMapper.selectpjazsxDotList(param);
        });
        CompletableFuture<List<BydBaoBiaoResult>> f2 = CompletableFuture.supplyAsync(() -> {
            return baseMapper.selectazjslDotList(param);
        });
        CompletableFuture<List<BydBaoBiaoResult>> f3 = CompletableFuture.supplyAsync(() -> {
            return baseMapper.selectazlDotList(param);
        });
        CompletableFuture<List<BizRegionEntity>> f4 = CompletableFuture.supplyAsync(() -> {
            return bizRegionDao.selectBatchIds(param.getProvinceIds());
        });
        CompletableFuture<Void> combinedFuture = CompletableFuture.allOf(f1, f2, f3, f4);
        // 获取所有结果
        try {
            combinedFuture.get();
            pjazsxList = f1.get();
            azjslList = f2.get();
            azlList = f3.get();
            bizRegionEntities = f4.get();
        } catch (InterruptedException | ExecutionException e) {
            return null;
            // 处理异常情况
        }
        List<BydBaoBiaoResult> results = dealWithDotList(pjazsxList, azjslList, azlList, bizRegionEntities);
        return R.ok().put("data", results);
    }


    @Override
    public R companyChangChengAuditNotice(AuditNoticeDTO auditNoticeDTO) {
        QueryWrapper<WorderInformationEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("worder_id", auditNoticeDTO.getWorderId());
        WorderInformationEntity worderInformation = getOne(wrapper);
        if (Objects.isNull(worderInformation)) {
            return R.error("工单不合法");
        }
        //查询工单信息
        WorderInformationAttributeEntity attributeEntity = worderInformationAttributeDao.selectOne(
                new QueryWrapper<WorderInformationAttributeEntity>()
                        .eq("attribute_code","CcAuditCancelOrder")
                        .eq("is_delete","0")
                        .eq("worder_id",worderInformation.getWorderId()));
        //调用推送CPIM接口
        if (attributeEntity!=null){
            worderInformationAttributeDao.updateDelete(worderInformation.getWorderId(),attributeEntity.getAttribute(),"CcAuditCancelOrder");
        }
        String content = Objects.equals(auditNoticeDTO.getFirstCheckFlag(), "01") ? "审核通过" : "审核不通过";
        addWorderRemarkLog(worderInformation, content + " " + auditNoticeDTO.getFirstCheckRemark(), "处理长城作废审核");
        String pushed = changChengWallClient.pushSubmitCancelOrder(worderInformation.getWorderId(), worderInformation.getCompanyOrderNumber(),
                worderInformation.getWorderNo(), auditNoticeDTO.getFirstCheckFlag(), auditNoticeDTO.getFirstCheckRemark());
        return StringUtils.isEmpty(pushed) ? R.ok() : R.error(pushed);
    }

    private void addWorderRemarkLog(WorderInformationEntity worderInformation, String content, String title) {
        WorderRemarkLogEntity worderRemarkLogEntity = new WorderRemarkLogEntity();
        worderRemarkLogEntity.setWorderNo(worderInformation.getWorderNo());
        worderRemarkLogEntity.setUserId(ConstantPool.BYD_OPERATOR);
        worderRemarkLogEntity.setUserName(ConstantPool.BYD_OPERATOR_NAME);
        worderRemarkLogEntity.setTitle(title);
        worderRemarkLogEntity.setContent(content);
        worderRemarkLogEntity.setCreateTime(new Date());
        worderRemarkLogDao.insert(worderRemarkLogEntity);
    }


    @Override
    public R companyReminderNoticeDispose(NoticeDisposeDTO dto) {
        QueryWrapper<WorderInformationEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("worder_id", dto.getWorderId());
        WorderInformationEntity worderInformation = getOne(wrapper);
        if (Objects.isNull(worderInformation)) {
            return R.error("工单不合法");
        }
        //查询工单信息
        WorderInformationAttributeEntity attributeEntity = worderInformationAttributeDao.selectOne(
                new QueryWrapper<WorderInformationAttributeEntity>()
                        .in("attribute_code", Lists.newArrayList("CcFirstReminderOrder","CcSecondReminderOrder"))
                        .eq("is_delete","0")
                        .eq("worder_id",worderInformation.getWorderId())
                        .orderByDesc("id").last("limit 1"));

        if (attributeEntity!=null){
            worderInformationAttributeDao.updateDelete(worderInformation.getWorderId(),attributeEntity.getAttribute(),attributeEntity.getAttributeCode());
        }
        SubmitReminderRequest reminderRequest = new SubmitReminderRequest();
        reminderRequest.setOrderNo(worderInformation.getCompanyOrderNumber());
        if (Objects.equals(attributeEntity.getAttributeCode(),"CcFirstReminderOrder")) {
            reminderRequest.setFirstReminderDealTime(parseDate(dto.getReminderDealTime()));
            reminderRequest.setFirstReminderRemark(dto.getReminderDealRemark());
        } else {
            reminderRequest.setSecondReminderDealTime(parseDate(dto.getReminderDealTime()));
            reminderRequest.setSecondReminderRemark(dto.getReminderDealRemark());
        }

        addWorderRemarkLog(worderInformation, dto.getReminderDealTime() + " " +dto.getReminderDealRemark(), "处理催单通知");
        String pushed = changChengWallClient.pushSubmitReminderOrder(reminderRequest, worderInformation.getWorderNo());
        return StringUtils.isEmpty(pushed) ? R.ok() : R.error(pushed);
    }

    public Date parseDate(String date) {
        if (com.youngking.lenmoncore.common.utils.StringUtils.isBlank(date)) {
            return null;
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            return simpleDateFormat.parse(date);
        } catch (ParseException e) {
            return null;
        }
    }

    private List<BydBaoBiaoResult> dealWithDotList(List<BydBaoBiaoResult> pjazsxList, List<BydBaoBiaoResult> azjslList, List<BydBaoBiaoResult> azlList, List<BizRegionEntity> bizRegionEntities) {
        List<BydBaoBiaoResult> results = new ArrayList<>();
        //便利省份
        for (BizRegionEntity pro : bizRegionEntities) {
            Set<Long> set = new HashSet<>();
            List<BydBaoBiaoResult> l1 = pjazsxList.stream().filter(pjazsx -> pjazsx.getProId().equals(pro.getId())).collect(Collectors.toList());
            List<BydBaoBiaoResult> l2 = azjslList.stream().filter(pjazsx -> pjazsx.getProId().equals(pro.getId())).collect(Collectors.toList());
            List<BydBaoBiaoResult> l3 = azlList.stream().filter(pjazsx -> pjazsx.getProId().equals(pro.getId())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(l1)) {
                set.addAll(l1.stream().map(r -> r.getDotId()).collect(Collectors.toList()));
            }
            if (!CollectionUtils.isEmpty(l2)) {
                set.addAll(l2.stream().map(r -> r.getDotId()).collect(Collectors.toList()));
            }
            if (!CollectionUtils.isEmpty(l3)) {
                set.addAll(l3.stream().map(r -> r.getDotId()).collect(Collectors.toList()));
            }
            Set<Long> filteredSet = set.stream()
                    .filter(item -> item != null)
                    .collect(Collectors.toSet());
            int size = filteredSet.size();
            int i = 0;
            for (Long dotId:filteredSet) {
                BydBaoBiaoResult result = new BydBaoBiaoResult();
                result.setType1(pro.getName());
                if(i == 0){
                    result.setType1Num(size);
                }else {
                    result.setType1Num(0);
                }
                i++;
                if(!CollectionUtils.isEmpty(l1)){
                    BydBaoBiaoResult b1 = l1.stream().filter(l -> dotId.equals(l.getDotId())).findFirst().orElse(null);
                    if(b1 != null){
                        result.setType2(b1.getType2());
                        result.setPjazsxSum(b1.getPjazsxSum());
                        result.setPjazsxSumHours(b1.getPjazsxSumHours());
                    }
                }
                if(!CollectionUtils.isEmpty(l2)) {
                    BydBaoBiaoResult b2 = l2.stream().filter(l -> dotId.equals(l.getDotId())).findFirst().orElse(null);
                    if (b2 != null) {
                        result.setType2(b2.getType2());
                        result.setAzjslSum24(b2.getAzjslSum24());
                        result.setAzjslSum(b2.getAzjslSum());
                    }
                }
                if(!CollectionUtils.isEmpty(l3)) {
                    BydBaoBiaoResult b3 = l3.stream().filter(l -> dotId.equals(l.getDotId())).findFirst().orElse(null);
                    if (b3 != null) {
                        result.setType2(b3.getType2());
                        result.setAzlSum(b3.getAzlSum());
                        result.setAzlSumConvey(b3.getAzlSumConvey());
                        result.setAzlSumCanal(b3.getAzlSumCanal());
                    }
                }
                results.add(result);
            }
        }
        return results;
    }

    @Override
    public R proCockpitData(BydBaobiaoParam param) {
        if (CollectionUtils.isEmpty(param.getBrandIds())) {
            List<Integer> brandIds = new ArrayList<>();
            brandIds.add(21);
            brandIds.add(22);
            brandIds.add(87);
            brandIds.add(88);
            brandIds.add(89);
            brandIds.add(90);
            brandIds.add(91);
            brandIds.add(121);
            param.setBrandIds(brandIds);
        }
        QueryWrapper<BizRegionEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("type", 1);
        List<BizRegionEntity> bizRegionEntities = null;
        //安装时效列表
        List<BydBaoBiaoResult> pjazsxList = null;
        //安装及时率
        List<BydBaoBiaoResult> azjslList = null;

        //安装率
        List<BydBaoBiaoResult> azlList = null;

        CompletableFuture<List<BydBaoBiaoResult>> f1 = CompletableFuture.supplyAsync(() -> {
            return baseMapper.selectpjazsxList(param);
        });
        CompletableFuture<List<BydBaoBiaoResult>> f2 = CompletableFuture.supplyAsync(() -> {
            return baseMapper.selectazjslList(param);
        });
        CompletableFuture<List<BydBaoBiaoResult>> f3 = CompletableFuture.supplyAsync(() -> {
            return baseMapper.selectazlList(param);
        });
        CompletableFuture<List<BizRegionEntity>> f4 = CompletableFuture.supplyAsync(() -> {
            return bizRegionDao.selectList(queryWrapper);
        });
        CompletableFuture<Void> combinedFuture = CompletableFuture.allOf(f1, f2, f3, f4);
        // 获取所有结果
        try {
            combinedFuture.get();
            pjazsxList = f1.get();
            azjslList = f2.get();
            azlList = f3.get();
            bizRegionEntities = f4.get();
        } catch (InterruptedException | ExecutionException e) {
            return null;
            // 处理异常情况
        }
        Map<String, List<BizRegionEntity>> map = bizRegionEntities.stream().collect(Collectors.groupingBy(BizRegionEntity::getRegione));
        List<BydBaoBiaoResult> results = dealWithList(pjazsxList, azjslList, azlList, map, bizRegionEntities.size());
        return R.ok().put("data", results);
    }

    private List<BydBaoBiaoResult> dealWithList(List<BydBaoBiaoResult> pjazsxList, List<BydBaoBiaoResult> azjslList, List<BydBaoBiaoResult> azlList, Map<String, List<BizRegionEntity>> map, Integer prodSize) {
        List<BydBaoBiaoResult> results = new ArrayList<>();
        //全国汇总
        BydBaoBiaoResult r = new BydBaoBiaoResult();
        r.setType1("全国");
        r.setType1Num(map.keySet().size() + prodSize + 1);
        r.setType2("全国汇总");
        r.setType2Num(1);
        r.setType3("全国汇总");
        if (!CollectionUtils.isEmpty(azjslList)) {
            r.setAzjslSum(azjslList.stream().mapToLong(BydBaoBiaoResult::getAzjslSum).sum());
            r.setAzjslSum24(azjslList.stream().mapToLong(BydBaoBiaoResult::getAzjslSum24).sum());
        }
        if (!CollectionUtils.isEmpty(pjazsxList)) {
            r.setPjazsxSum(pjazsxList.stream().mapToLong(BydBaoBiaoResult::getPjazsxSum).sum());
            r.setPjazsxSumHours(pjazsxList.stream().mapToLong(BydBaoBiaoResult::getPjazsxSumHours).sum());
        }
        if (!CollectionUtils.isEmpty(azlList)) {
            r.setAzlSum(azlList.stream().mapToLong(BydBaoBiaoResult::getAzlSum).sum());
            r.setAzlSumCanal(azlList.stream().mapToLong(BydBaoBiaoResult::getAzlSumCanal).sum());
            r.setAzlSumConvey(azlList.stream().mapToLong(BydBaoBiaoResult::getAzlSumConvey).sum());
        }

        results.add(r);

        for (String key : map.keySet()) {
            List<BizRegionEntity> bizRegionEntities = map.get(key);
            List<Long> prodIds = bizRegionEntities.stream().map(b -> b.getId()).collect(Collectors.toList());
            //大区汇总
            BydBaoBiaoResult result = new BydBaoBiaoResult();
            result.setType1("全国");
            result.setType1Num(0);
            result.setType2(key + "汇总");
            result.setType2Num(bizRegionEntities.size() + 1);
            result.setType3(key + "汇总");
            List<BydBaoBiaoResult> l1 = pjazsxList.stream().filter(p -> prodIds.contains(p.getProId())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(l1)) {
                result.setPjazsxSum(l1.stream().mapToLong(BydBaoBiaoResult::getPjazsxSum).sum());
                result.setPjazsxSumHours(l1.stream().mapToLong(BydBaoBiaoResult::getPjazsxSumHours).sum());
            }
            List<BydBaoBiaoResult> l2 = azjslList.stream().filter(p -> prodIds.contains(p.getProId())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(l2)) {
                result.setAzjslSum(l2.stream().mapToLong(BydBaoBiaoResult::getAzjslSum).sum());
                result.setAzjslSum24(l2.stream().mapToLong(BydBaoBiaoResult::getAzjslSum24).sum());
            }
            List<BydBaoBiaoResult> l3 = azlList.stream().filter(p -> prodIds.contains(p.getProId())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(l3)) {
                result.setAzlSum(l3.stream().mapToLong(BydBaoBiaoResult::getAzlSum).sum());
                result.setAzlSumCanal(l3.stream().mapToLong(BydBaoBiaoResult::getAzlSumCanal).sum());
                result.setAzlSumConvey(l3.stream().mapToLong(BydBaoBiaoResult::getAzlSumConvey).sum());
            }
            results.add(result);
            //各个省份的数据
            for (BizRegionEntity region : bizRegionEntities) {
                BydBaoBiaoResult result1 = new BydBaoBiaoResult();
                result1.setType1("全国");
                result1.setType1Num(0);
                result1.setType2(key + "汇总");
                result1.setType2Num(0);
                result1.setType3(region.getName());
                result1.setProId(region.getId());
                List<BydBaoBiaoResult> l11 = pjazsxList.stream().filter(p -> p.getProId().equals(region.getId())).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(l11)) {
                    result1.setPjazsxSum(l11.stream().mapToLong(BydBaoBiaoResult::getPjazsxSum).sum());
                    result1.setPjazsxSumHours(l11.stream().mapToLong(BydBaoBiaoResult::getPjazsxSumHours).sum());
                }
                List<BydBaoBiaoResult> l22 = azjslList.stream().filter(p -> p.getProId().equals(region.getId())).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(l22)) {
                    result1.setAzjslSum(l22.stream().mapToLong(BydBaoBiaoResult::getAzjslSum).sum());
                    result1.setAzjslSum24(l22.stream().mapToLong(BydBaoBiaoResult::getAzjslSum24).sum());
                }
                List<BydBaoBiaoResult> l33 = azlList.stream().filter(p -> p.getProId().equals(region.getId())).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(l33)) {
                    result1.setAzlSum(l33.stream().mapToLong(BydBaoBiaoResult::getAzlSum).sum());
                    result1.setAzlSumCanal(l33.stream().mapToLong(BydBaoBiaoResult::getAzlSumCanal).sum());
                    result1.setAzlSumConvey(l33.stream().mapToLong(BydBaoBiaoResult::getAzlSumConvey).sum());
                }
                results.add(result1);
            }
        }
        return results;
    }

    /**
     * 查询已开票工单
     *
     * @param allWorderNos
     * @return
     */
    @Override
    public List<WorderInformationEntity> getInvoicedWordersByWorderNoList(List<String> allWorderNos) {
        if (Objects.isNull(allWorderNos) || allWorderNos.isEmpty()) {
            return Collections.emptyList();
        }
        return this.lambdaQuery()
                .select(WorderInformationEntity::getWorderId, WorderInformationEntity::getWorderNo)
                .in(WorderInformationEntity::getWorderNo, allWorderNos)
                .isNotNull(WorderInformationEntity::getInvoiceId)
                .list();
    }

    @Override
    public List<WorderInformationEntity> getFinishedWorders(List<String> worderNos) {
        if (Objects.isNull(worderNos) || worderNos.isEmpty()) {
            return Collections.emptyList();
        }
        return this.lambdaQuery()
                .select(WorderInformationEntity::getWorderId, WorderInformationEntity::getWorderNo, WorderInformationEntity::getWorderStatus,
                        WorderInformationEntity::getWorderNo, WorderInformationEntity::getWorderExecStatus)
                .in(WorderInformationEntity::getWorderNo, worderNos)
                .eq(WorderInformationEntity::getWorderExecStatus, 17)
                .list();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void rollbackWorderStatusTo16(List<WorderInformationEntity> finishedWorders) {
        if (Objects.isNull(finishedWorders) || finishedWorders.isEmpty()) {
            return;
        }
        List<Integer> worderIds = finishedWorders.stream().map(WorderInformationEntity::getWorderId).collect(Collectors.toList());
        if (worderIds.isEmpty()) {
            return;
        }
        boolean countOfWorderInformation = this.lambdaUpdate()
                .set(WorderInformationEntity::getWorderExecStatus, 16)
                .set(WorderInformationEntity::getWorderStatus, 2)
                .in(WorderInformationEntity::getWorderId, worderIds)
                .update();
        QueryWrapper<FlowWorderEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("worder_id", worderIds);
        FlowWorderEntity flowWorderEntity = new FlowWorderEntity();
        flowWorderEntity.setNextFlowChildCode("anzhuangziliaowuwudaishangchuancheqi");
        int countOfFlowWorder = flowWorderDao.update(flowWorderEntity, queryWrapper);
        boolean countOfWorderWaitAccount = worderWaitAccountService.lambdaUpdate()
                .in(WorderWaitAccountEntity::getWorderId, worderIds)
                .remove();
        log.info("rollbackWorderStatusTo16 success, countOfWorderInformation:{}, countOfFlowWorder:{}, countOfWorderWaitAccount:{}",
                countOfWorderInformation, countOfFlowWorder, countOfWorderWaitAccount);

        // 正常情况下应该是小于或等于的
        if (countOfFlowWorder > worderIds.size()) {
            throw new RRException("Error occurred, rollback");
        }

        List<WorderOperationRecodeEntity> recordList = finishedWorders.stream().map(worderInformationEntity -> {
            WorderOperationRecodeEntity worderOperationRecodeEntity = new WorderOperationRecodeEntity();
            SysUserEntity userEntity = (SysUserEntity) SecurityUtils.getSubject().getPrincipal();
            worderOperationRecodeEntity.setUserId(userEntity.getUserId());
            worderOperationRecodeEntity.setOperationUser(userEntity.getUsername());
            worderOperationRecodeEntity.setRecord(userEntity.getUsername() + "回退至安装资料无误待上传车企");
            worderOperationRecodeEntity.setWorderNo(worderInformationEntity.getWorderNo());
            worderOperationRecodeEntity.setCreateTime(DateUtils.getCurrentTime());
            worderOperationRecodeEntity.setWorderStatus(String.valueOf(worderInformationEntity.getWorderStatus()));
            worderOperationRecodeEntity.setWorderExecStatus(String.valueOf(worderInformationEntity.getWorderExecStatus()));
            return worderOperationRecodeEntity;
        }).collect(Collectors.toList());
        worderOperationRecodeService.saveBatch(recordList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateWorderTemplate(String templateId, List<String> worderNoList) {
        if (Objects.isNull(worderNoList) || worderNoList.isEmpty()) {
            return;
        }
        String createTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
        this.lambdaUpdate()
                .set(WorderInformationEntity::getTemplateId, templateId)
                .in(WorderInformationEntity::getWorderNo, worderNoList)
                .update();
        List<WorderOperationRecodeEntity> records = worderNoList.stream().map(worderNo -> {
            WorderOperationRecodeEntity worderOperationRecodeEntity = new WorderOperationRecodeEntity();
            SysUserEntity userEntity = (SysUserEntity) SecurityUtils.getSubject().getPrincipal();
            worderOperationRecodeEntity.setUserId(userEntity.getUserId());
            worderOperationRecodeEntity.setOperationUser(userEntity.getUsername());
            worderOperationRecodeEntity.setRecord(userEntity.getUsername() + "修改工单模板ID->" + templateId);
            worderOperationRecodeEntity.setWorderNo(worderNo);
            worderOperationRecodeEntity.setCreateTime(createTime);
            return worderOperationRecodeEntity;
        }).collect(Collectors.toList());
        worderOperationRecodeService.saveBatch(records);
    }

    public void saveWorderRemark(String worderNo, String title, String content, Integer userId) {
        Date currentDate = new Date();
        // 使用Calendar来增加一秒
        long time = currentDate.getTime();
        if (content.contains("服务经理")) {
            time = time + 1000;
        } else if (content.contains("网点")) {
            time = time + 2000;
        }
        WorderRemarkLogEntity worderRemarkLogEntity = new WorderRemarkLogEntity()
                .setWorderNo(worderNo)
                .setTitle(title)
                .setContent(content)
                .setCreateTime(new Date(time))
                .setUserId(userId != null ? userId.longValue() : null);
        worderRemarkLogService.save(worderRemarkLogEntity);
    }

}
