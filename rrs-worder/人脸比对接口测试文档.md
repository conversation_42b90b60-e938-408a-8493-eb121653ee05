# 人脸比对接口测试文档

## 接口概述

本接口用于人脸比对功能，前端调用后端接口，参数为人脸照片的URL，后端接收到URL后与当前服务兵的身份证照片做比对，调用阿里云人脸比对接口，返回比对结果给前端。

## 配置要求

### 1. 环境变量配置

在服务器环境中设置以下环境变量：

```bash
export ALIBABA_CLOUD_ACCESS_KEY_ID=your_actual_access_key_id
export ALIBABA_CLOUD_ACCESS_KEY_SECRET=your_actual_access_key_secret
```

### 2. 阿里云权限配置

确保阿里云账号具有以下权限：
- AliyunVIAPIFullAccess（视觉智能开放平台完整权限）

## 接口详情

### 1. 人脸比对接口

**接口地址：** `POST /app/face/compare`

**请求参数：**
```json
{
    "faceImageUrl": "https://example.com/face.jpg",
    "attendantId": 123  // 可选，不传则使用当前登录用户
}
```

**响应示例：**
```json
{
    "msg": "success",
    "code": 0,
    "data": {
        "isSamePerson": true,
        "similarity": 85.5,
        "confidence": 90.2,
        "statusCode": "200",
        "statusMessage": "success",
        "attendantName": "张三",
        "attendantId": 123,
        "timestamp": 1640995200000
    }
}
```

### 2. 获取身份证照片URL接口（测试用）

**接口地址：** `GET /app/face/idcard-url`

**响应示例：**
```json
{
    "msg": "success",
    "code": 0,
    "idCardUrl": "https://example.com/idcard.jpg"
}
```

### 3. 根据服务兵ID获取身份证照片URL接口（测试用）

**接口地址：** `GET /app/face/idcard-url/{attendantId}`

**响应示例：**
```json
{
    "msg": "success",
    "code": 0,
    "idCardUrl": "https://example.com/idcard.jpg"
}
```

## 测试步骤

### 1. 使用Postman测试

1. **登录获取Token**
   - 先调用登录接口获取用户Token
   - 确保登录用户是服务兵角色

2. **测试获取身份证照片URL**
   ```
   GET /app/face/idcard-url
   Headers: 
   - Authorization: Bearer {token}
   ```

3. **测试人脸比对**
   ```
   POST /app/face/compare
   Headers: 
   - Authorization: Bearer {token}
   - Content-Type: application/json
   
   Body:
   {
       "faceImageUrl": "https://example.com/test-face.jpg"
   }
   ```

### 2. 测试数据准备

1. **准备测试图片**
   - 准备一张人脸照片，上传到可访问的URL
   - 确保服务兵账号已上传身份证照片

2. **测试场景**
   - 同一人照片比对（应返回高相似度）
   - 不同人照片比对（应返回低相似度）
   - 无效URL测试
   - 非人脸图片测试

## 错误处理

### 常见错误码

- `401`: 用户未登录
- `404`: 服务兵不存在或身份证照片不存在
- `500`: 服务器内部错误或阿里云API调用失败

### 错误响应示例

```json
{
    "msg": "服务兵身份证照片不存在",
    "code": 404,
    "data": {
        "isSamePerson": false,
        "similarity": 0.0,
        "confidence": 0.0,
        "statusCode": "404",
        "statusMessage": "服务兵身份证照片不存在",
        "timestamp": 1640995200000
    }
}
```

## 注意事项

1. **图片格式要求**
   - 支持JPG、PNG格式
   - 图片大小建议不超过2MB
   - 图片中应包含清晰的人脸

2. **相似度阈值**
   - 当前设置为80%，可根据实际需求调整
   - 相似度越高，表示越可能是同一人

3. **性能考虑**
   - 阿里云API有调用频率限制
   - 建议添加缓存机制避免重复比对

4. **安全考虑**
   - 确保图片URL的安全性
   - 避免暴露敏感的身份证照片URL

## 部署说明

1. **依赖检查**
   - 确保Maven依赖已正确添加
   - 检查阿里云SDK版本兼容性

2. **配置检查**
   - 验证application.yml配置正确
   - 确保环境变量已设置

3. **权限检查**
   - 验证阿里云账号权限
   - 检查服务兵数据完整性
